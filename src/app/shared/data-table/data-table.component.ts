import {
  Component,
  OnInit,
  Input,
  Output,
  OnChanges,
  SimpleChanges,
  EventEmitter,
  ViewChild,
  TemplateRef,
  Inject
} from "@angular/core";
import { Router } from "@angular/router";
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { EditSKUData } from "../../app-services/edit-sku-data-service";
import { AuthenticationHelper } from "../../helpers/authentication";
import { MatDialog } from "@angular/material/dialog";
import { CommonModule, DOCUMENT } from '@angular/common';

import { GlobalEvents } from "../../helpers/global.events";
import { NgxPaginationModule } from "ngx-pagination";
import { NgClass, NgFor, NgIf } from "@angular/common";
import { MatButtonModule } from '@angular/material/button';
import { DashboardService } from "src/app/app-services/dashboard.service";
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatDialogModule } from '@angular/material/dialog';
import { TargetModalComponent } from "src/app/modals/target-modal/target-modal.component";
import { AddBonusPopupComponent } from "../bonus-percentage-dialog/bonus-percentage-dialog.component";
import { MatTooltipModule } from '@angular/material/tooltip';
import { ViewDocumentsModalComponent } from "src/app/modals/view-documents-modal/view-documents-modal.component";
import { RewardPointsService } from "src/app/app-services/reward-points-service";
import { UserService } from "src/app/app-services/user-service";
import { catchError, forkJoin, map, Observable, of, tap } from "rxjs";
import { BaThemeSpinner } from "../../theme/services/baThemeSpinner/baThemeSpinner.service";
import { Utility } from "src/app/shared/utility/utility";

interface DocumentEntry {
  name: string;
  url: string;
  type: string;
  button: string;
  contentType?: string;
}


@Component({
  selector: "dynamic-table",
  templateUrl: "data-table.component.html",
  styleUrls: ["data-table.component.scss"],
  imports: [NgClass, MatDialogModule, NgFor, NgIf, NgxPaginationModule, MatButtonModule, MatSlideToggleModule, TargetModalComponent, CommonModule, MatTooltipModule, ViewDocumentsModalComponent],
  standalone: true
})
export class DynamicTableComponent implements OnInit, OnChanges {
 
  iframeUrl: string | null = null;
  currentDocumentType: string | null = null;
  selectedData: any = null;
  path: string;
  statusData: any;
  totalItems: number;
  @Input() tableConfiguration: any = {
    showPagination: false,
    perPage: 15,
    totalRecordCount: 0,
    currentPage: 1,
    showActionsColumn: false,
    actionsColumnName: "Actions",
    showViewDownload: false, 
    viewDownloadColumnName: "View/Download", 
    noDataMessage: "No data found",
    editBox: false,
    productIcon: false,
    showEdit: true,
    showStatus: false,
    changeStatus: false,
    isApprove: false,
    isRejected: false,
    showUpload:false,
  };
  previousModalState = false;
  updatedData: any = {};
  
  @Input() tableData: any = [];
  @Input() tableHeads: Array<string> = new Array<string>();
  @Input() tableColName: Array<string> = new Array<string>();
  @Input() showIndex: any = { index: null };
  @Input() tableRowData: any = [];
  @Output() pageChange: any = new EventEmitter();
  @Output() brandDetails: any = new EventEmitter();
  @Output() onRowEdit: any = new EventEmitter();
  @Output() deleteRow = new EventEmitter<any>();  
  @Output() onStatusChange: any = new EventEmitter();
  @Output() toggleEdit = new EventEmitter<number>();
  @Output() emitInvoiceEdit = new EventEmitter<Boolean>();
  @Output() uploadAgreement:any = new EventEmitter();
  @Output() rejectInvoice:any = new EventEmitter();
  @Output() editFormClose = new EventEmitter<any>();
  @Output() onApproveTarget = new EventEmitter<any>();
  @Output() onRejectTarget = new EventEmitter<any>();
  @Output() viewProduct = new EventEmitter<any>();
  @Output() downloadAgreement = new EventEmitter<any>();
  @Output() onViewInvoice = new EventEmitter<any>();
  @Output() onDownloadInvioce = new EventEmitter<any>();
  
  public tableColNameGenerated: Array<any> = new Array<any>();
  private isTableColNameSet: Boolean = false;
  innerData: boolean = false;
  isAdmin: boolean = false;
  @ViewChild("changeStatus") changeStatus!: TemplateRef<any>;
  @ViewChild('test') test!: TemplateRef<any>;

  @ViewChild("statusConfirm") statusConfirm: any;

  // Add this property to track file type
  currentDocumentFileType: 'pdf' | 'image' | 'other' = 'pdf';

  color = 'green';
  checked = true;
  disabled = false;
  brandData: any;
  expandedRowIndex: number | null = null;
  isActive: any;
  activeTabTitle: string = '';
  targetModalOpen = false;
  targetModalMode: 'add' | 'edit' = 'add';
  isViewPDFModalOpen = false;
  selectedDocuments: DocumentEntry[] = [];
  startDate: Date = new Date(new Date().getMonth() <= 2 ? 
    new Date().getFullYear() - 1 : new Date().getFullYear(), 3, 1);
  endDate: Date = new Date(new Date().getMonth() <= 2 ? 
    new Date().getFullYear() : new Date().getFullYear() + 1, 2, 31);

  targetDefaults: {
    rootRfcId: string;
    leaderName: string;
    totalTarget: number | null;
    id: number | null;
    cpPercent: number | null;
    cpAmount: number | null;
    nppPercent: number | null;
    nppAmount: number | null;
    isActive: boolean;
    grower: boolean;
    startDate: Date;
    endDate: Date;
  } = {
    rootRfcId: '',
    leaderName: '',
    totalTarget: null,
    id: null,
    cpPercent: null,
    cpAmount: null,
    nppPercent: null,
    nppAmount: null,
    isActive: false,
    grower: false,
    startDate: this.startDate,
    endDate: this.endDate,
  };
  cdRef: any;
  
  isDocumentLoading = false;
  sortColumn: string | null = null;
  sortDirection: 'asc' | 'desc' = 'asc';
  private originalTableData: any[] = [];

  constructor(
    public dialog: MatDialog,
    private router: Router,
    private editSkuDataService: EditSKUData,
    private events: GlobalEvents,
    private userService: UserService,
    private rewardService: RewardPointsService,
    private dashboardService: DashboardService,
    private sanitizer: DomSanitizer,
    private spinner: BaThemeSpinner,
    @Inject(Utility) private utility: Utility,
    @Inject(DOCUMENT) private document: Document
  ) {
    this.path = this.router.url;
    this.isAdmin = parseInt(AuthenticationHelper.getRoleID() ?? "") === 1;
    this.totalItems = this.tableConfiguration?.totalRecordCount
    this.events.getChangedStatusText().subscribe((item) => {
      if (item) {
        this.activeTabTitle = item;
      }
    });
  }
  ngOnInit() {
    // Store original data when component initializes
    if (this.tableData && this.tableData.length > 0) {
      this.originalTableData = JSON.parse(JSON.stringify(this.tableData));
    }
  }
  ngAfterViewInit() {
    this.events.onUserStatusChange.subscribe((item) => {
      if (item) {
      }
    });
  }
  
  ngOnChanges(changes: SimpleChanges) {
    if (changes["tableHeads"] || changes["tableConfiguration"]) {
      if (this.tableHeads.length) {
        // Create a new array to avoid mutating the input directly
        const updatedHeads = [...this.tableHeads];
  
        // Add View/Download column if enabled and not already present
        if (this.tableConfiguration.showViewDownload && !updatedHeads.includes('View/Download')) {
          updatedHeads.push('View/Download');
        }
  
        // Add Actions column if enabled and not already present
        if (this.tableConfiguration.showActionsColumn && 
            this.tableConfiguration.actionsColumnName && 
            !updatedHeads.includes(this.tableConfiguration.actionsColumnName)) {
          updatedHeads.push(this.tableConfiguration.actionsColumnName);
        }
  
        // Update tableHeads with the new array
        this.tableHeads = updatedHeads;
      }
    }
    if (changes["tableData"] && changes["tableData"].currentValue) {
      // Store a deep copy of the original data when it changes
      this.originalTableData = JSON.parse(JSON.stringify(changes["tableData"].currentValue));
      
      if (!this.isTableColNameSet) {
        if (this.tableData && this.tableData.length > 0) {
          this.tableColNameGenerated = this.getKeys(this.tableData[0]);
        }
      }
    }

    if (changes["tableColName"]) {
      if (this.tableColName.length > 0) {
        this.tableColNameGenerated = this.tableColName;
        this.isTableColNameSet = true;
      }
    }

    if (changes["tableConfiguration"]) {
      // Handle any tableConfiguration changes if needed
    }
  }
  private isHeadAndColLengthSame(
    head: Array<String>,
    col: Array<String>
  ): Boolean {
    return head.length === col.length;
  }

  private getKeys(value: any): Array<String> {
    return Object.keys(value);
  }

  public getPageData(page: any) {
    this.pageChange.emit(page);
  }

  public editRowData(data: any, index: any,): void {
    switch (this.path) {
      case "/zones":
        this.router.navigate(["zones/edit-zone"], {
          queryParams: { id: data.code },
        });
        break;
      case "/users":
        break;
      case "/regions":
        this.router.navigate(["regions/edit-region"], {
          queryParams: { id: data.code },
        });
        break;
      case "/rewards-points":
        this.router.navigate(["rewards-points/edit-rewards-points"], {
          queryParams: { id: data.id },
        });
        break;
      case "/target-management":
        this.openSetTargetData(data);
        break;
      case "/invoices":
        this.emitInvoiceEdit.emit(data);
        break;
    }
    if (this.path.includes("/product-catalog/sku")) {
      this.showIndex.index = index;
    }
    this.onRowEdit.emit(data);
  }

  onConfirmStatusChange(): void {
    // Add encryption/decryption handling for status change
    this.onStatusChange.emit(this.statusData);
    this.dialog.closeAll();
    this.dashboardService._disabledSidebar.emit(false);
  }

  valueCol(event: any, col: any): void {
    this.updatedData[col] = event.target.value;
  }
  updateRowData(): void {
    this.editSkuDataService.rowData.next(this.updatedData);
    this.updatedData = {};
  }

  updateFalse(): void {
    this.tableConfiguration.editBox = false;
    this.showIndex.index = null;
  }

  productDetails(data: any): void {
    this.brandDetails.emit(data);
  }

  /**
   * Called to get the pdf view details
   * @param data
   */
   // Add interface at the top of your component

   viewPDFData(data: any, colName: string): void {
    if(this.path.includes("/invoices") && colName === 'product'){
      this.viewProduct.emit(data);
      return;
    }
    this.selectedData = data;
    const hasUrls = data && (data.idUrl || data.rfcDocUrl || data.addressDocUrl);
    
    if (hasUrls) {
      this.spinner.show();
      
      this.selectedDocuments = [];
      const documentRequests: Observable<DocumentEntry | null>[] = [];
      
      const createDocumentRequest = (url: string | undefined, type: string, name: string): Observable<DocumentEntry | null> | null => {
        if (!url) {
          console.warn(`No URL provided for ${name}`);
          return null;
        }
                
        return this.userService.showDocument(url).pipe(
          catchError(error => {
            console.error(`Error fetching ${name}:`, error);
            return of(null);
          }),
          map(response => {
            return response ? {
              name: name,
              url: response.url,
              type: type,
              contentType: response.contentType,
              button: `View ${name}`
            } : null;
          })
        );
      };
      
      const addRequest = (url: string | undefined, type: string, name: string) => {
        const req = createDocumentRequest(url, type, name);
        if (req) {
          documentRequests.push(req);
        }
      };
      
      addRequest(data.idUrl, 'id', 'ID');
      addRequest(data.rfcDocUrl, 'rfc', 'RFC');
      addRequest(data.addressDocUrl, 'address', 'Address');
      
      if (documentRequests.length) {
        forkJoin(documentRequests).subscribe({
          next: (documents: (DocumentEntry | null)[]) => {
            this.spinner.hide();
            
            this.selectedDocuments = documents.filter((doc): doc is DocumentEntry => doc !== null);
            
            if (this.selectedDocuments.length > 0) {
              this.isViewPDFModalOpen = true;
              this.cdRef?.detectChanges();
              setTimeout(() => this.cdRef?.markForCheck(), 0);
            }
          },
          error: error => {
            this.spinner.hide();
            console.error('Error during document loading:', error);
          }
        });
      } else {
        this.spinner.hide();
      }
    }
  }
 
  getDocumentTitle(): string {
    switch(this.currentDocumentType) {
      case 'id': return 'ID Document';
      case 'rfc': return 'RFC Document';
      case 'address': return 'Address Proof';
      default: return 'Document Preview';
    }
  }
  
  

  handleCloseIframe() {
    this.iframeUrl = null;
    const backdrop = document.getElementById('iframe-backdrop');
    if (backdrop) {
      document.body.removeChild(backdrop);
    }
    if (this.previousModalState) {
      this.isViewPDFModalOpen = true;
    }
    this.previousModalState = false;
  }

  colData(event: any) { }

  openSetTargetData(data: any) {
    this.rewardService.getTargetById(data?.targetId).subscribe({
      next: (response: any) => {
        try {
          // First, check if response is a string and try to parse it
          let parsedResponse = response;
          if (typeof response === 'string') {
            try {
              parsedResponse = JSON.parse(response);
            } catch (e) {
              // If parsing fails, try to decrypt first
              try {
                const decrypted = this.utility.decrypt(response);
                parsedResponse = JSON.parse(decrypted);
              } catch (decryptError) {
                console.error('Error decrypting response:', decryptError);
                parsedResponse = response;
              }
            }
          }
          
          // Check if response has encryptedBody property
          let targetData;
          if (parsedResponse && parsedResponse.encryptedBody) {
            // Decrypt the encryptedBody
            const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
            targetData = JSON.parse(decrypted);
          } else {
            targetData = parsedResponse;
          }
          
          const rootData = targetData ?? {};
          const targetDetails = targetData?.target ?? {};

          this.targetModalMode = "edit";

          const totalTarget = targetDetails.amount ?? null;
          const cpAmount = targetDetails.cropProtectionAmount ?? null;

          const cpPercent = (totalTarget !== null && cpAmount !== null)
            ? Math.round((cpAmount / totalTarget) * 100)
            : null;
          
          const nppPercent = (cpPercent !== null)
            ? 100 - cpPercent
            : null;
          
          const nppAmount = (totalTarget !== null && cpAmount !== null)
            ? totalTarget - cpAmount
            : null;

          const {
            leaderName = rootData?.leaderName,
            targetId = null,
            taxId = null,
            grower = false,
            startDate = this.startDate,
            endDate = this.endDate,
            isActive = false
          } = targetDetails;

          this.targetDefaults = {
            leaderName,
            id: rootData?.id,
            rootRfcId: rootData?.leaderRfc,
            totalTarget,
            cpPercent,   
            cpAmount,
            nppPercent, 
            nppAmount,
            grower,
            startDate,
            endDate,
            isActive
          };

          this.targetModalOpen = true;
        } catch (error) {
          console.error('Error processing target data:', error);
          this.spinner.hide();
        }
      },
      error: (error: any) => {
        console.error('Error processing target data:', error);
        this.spinner.hide();
      }
    });
  }
  
  

  /**
   * Called to get the pdf view details
   * @param data
   */
  // viewPDFData(data: any): void {
  //   this.isViewPDFModalOpen = true;
  // }

  // colData(event: any) { }


  onStatusClick(data: any, event: MouseEvent) {
    if (!data) return;

    this.isActive = data.is_active;
    this.statusData = { ...data };

    if (!this.isActive && this.path.includes('/product-catalog')) {
      this.handleProductCatalogStatus(event);
      return;
    }
    // if (this.activeTabTitle === "Leaders") {
    //   if (this.isActive) {
    //     this.openDialogBox();
    //   } else {
    //     this.targetModalMode = "add";
    //     this.targetModalOpen = true;
    //     this.targetDefaults = {
    //       leaderName: data?.id || null,
    //       id: data?.leaderId || null,
    //       rootRfcId: data?.taxId || '',
    //       totalTarget: data?.amount || null,
    //       cpPercent: data?.cropProtectionPercent,
    //       cpAmount: data?.cropProtectionAmount,
    //       nppPercent: data?.nppPercent,
    //       nppAmount: data?.nppAmount,
    //       grower: data?.grower || false,
    //       startDate: data?.startDate || this.startDate,
    //       endDate: data?.endDate || this.endDate,
    //       isActive: data?.isActive || false
    //     };
    //   }
    //   return;
    // }

    this.openDialogBox();
  }
  onShowTargetClick(data: any, event: MouseEvent) {
    if (!data) return;
    if (this.activeTabTitle === "Leaders") {
      if(data.targetStatus === 'NOT_SET'){
        this.targetModalMode = "add";
        this.targetModalOpen = true;
        this.targetDefaults = {
          leaderName: data?.leader || null,
          id: data?.leaderId || null,
          rootRfcId: data?.taxId || '',
          totalTarget: data?.amount || null,
          cpPercent: data?.cropProtectionPercent,
          cpAmount: data?.cropProtectionAmount,
          nppPercent: data?.nppPercent,
          nppAmount: data?.nppAmount,
          grower: data?.grower || false,
          startDate: data?.startDate || this.startDate,
          endDate: data?.endDate || this.endDate,
          isActive: data?.isActive || false
        }
      }
      return;
    }

    // this.openDialogBox();
  }

  private handleProductCatalogStatus(event: MouseEvent): void {
    const dialogRef = this.dialog.open(AddBonusPopupComponent, {
      width: '400px',
      data: {
        currentBonus: this.statusData.bonusPercentage
      },
      panelClass: ['confirm-dialog-container']
    });

    dialogRef.afterClosed().subscribe(result => {
      if (!result) return;

      this.statusData = {
        id: this.statusData.id,
        active: result.active,
        bonusPercentage: Number(result.bonusPercentage),
        startDate: new Date(result.startDate),
        endDate: new Date(result.endDate)
      };

      this.openDialogBox();
    });
  }

  // private calculateDialogPosition(event: MouseEvent): { top: string, left: string } {
  //   const viewportHeight = window.innerHeight;
  //   const dialogHeight = 400;
  //   const clickY = event.clientY;

  //   let topPosition = clickY;
  //   if (clickY + dialogHeight > viewportHeight) {
  //     topPosition = viewportHeight - dialogHeight - 20;
  //   }

  //   const leftPosition = Math.max(400, event.clientX - 400);

  //   return {
  //     top: `${topPosition}px`,
  //     left: `${leftPosition - 20}px`
  //   };
  // }

  handleTargetSubmit(data: any) {
    this.targetModalOpen = false;
    this.editFormClose.emit(data);
  }

  handleViewDocument(type: string) {
    if (!['id', 'rfc', 'address'].includes(type)) {
      console.error('Invalid document type:', type);
      return;
    }
    
    const doc = this.selectedDocuments.find(d => d.type === type);
    if (doc) {
      this.currentDocumentType = doc.type;
      this.previousModalState = this.isViewPDFModalOpen;
      this.isViewPDFModalOpen = false;
      
      const backdrop = document.createElement('div');
      backdrop.id = 'iframe-backdrop';
      backdrop.style.position = 'fixed';
      backdrop.style.top = '0';
      backdrop.style.left = '0';
      backdrop.style.width = '100%';
      backdrop.style.height = '100%';
      backdrop.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
      backdrop.style.zIndex = '9998';
      document.body.appendChild(backdrop);
      backdrop.addEventListener('click', () => {
        this.handleCloseIframe();
      });
      
      this.determineFileType(doc.url, doc.contentType);
      setTimeout(() => {
        this.iframeUrl = doc.url;
      }, 100);
    } else {
      console.warn('Document not found for type:', type);
    }
  }

  private determineFileType(url: string, contentType?: string): void {
    if (contentType) {
      if (contentType.startsWith('image/')) {
        this.currentDocumentFileType = 'image';
      } else if (contentType === 'application/pdf') {
        this.currentDocumentFileType = 'pdf';
      } else {
        this.currentDocumentFileType = 'other';
      }
    } else {
      if (url.includes('data:image/') || url.match(/\.(jpe?g|png|gif|bmp)$/i)) {
        this.currentDocumentFileType = 'image';
      } else if (url.includes('data:application/pdf') || url.endsWith('.pdf')) {
        this.currentDocumentFileType = 'pdf';
      } else {
        this.currentDocumentFileType = 'other';
      }
    }
  }

  getDocumentFileType(): string {
    return this.currentDocumentFileType;
  }

  onDocumentLoaded() {
    this.isDocumentLoading = false;
    this.cdRef?.detectChanges();
  }
  

  handleDeleteClick(rowData: any) {
    this.deleteRow.emit(rowData);
  }
  openDialogBox() {
    this.dialog.open(this.test, {
      disableClose: false,
      panelClass: "confirm-dialog-container",
      data: this.statusData,
      hasBackdrop: false,
      autoFocus: false,
    });
  }

  onCloseStatus(): void {
    this.dialog.closeAll();
    this.dashboardService._disabledSidebar.emit(false);
  }
  toggleEditImage(index: number, event: Event) {
    event.stopPropagation();
    this.toggleEdit.emit(index);
  }

  toggleRowExpansion(index: number): void {
    this.expandedRowIndex = this.expandedRowIndex === index ? null : index;
  }
  
  onIconClick(colName: string, data: any): void {
    if (colName === 'product') {
      this.brandDetails.emit(data); 
    }
  }

  getStatusColor(status: string): string {
    switch (status?.toLowerCase()) {
      case 'approved':
        return '#FF8033';
      case 'pending':
        return '#ffcc12'; 
      case 'rejected':
        return '#6f7070'; 
      case 'active':
        return '#FF8033'; 
      case 'expired':
        return '#6f7070'; 
      default:
        return ''; 
    }
  }

  onViewClick(data:any){ 
    this.onViewInvoice.emit(data);  
  }

  onDownloadClick(data:any){ 
    if (this.path.includes("/users")) {
      this.downloadAgreement.emit(data)
    }
    if (this.path.includes("/invoice")) {
      this.onDownloadInvioce.emit(data)
    }
  }

  onUploadClick(data:any){    
    this.uploadAgreement.emit(data); 
  }
  onReject(data:any){
    this.rejectInvoice.emit(data); 
  }
  approveTarget(data: any) {
    this.selectedData = data;
    this.onApproveTarget.emit(data);
  }
  rejectTarget(data: any) {
    this.onRejectTarget.emit(data);
  }

  getSafeUrl(url: string | null): SafeResourceUrl {
    if (!url) {
      return this.sanitizer.bypassSecurityTrustResourceUrl('about:blank');
    }
    
    // For blob URLs, we need to ensure they're properly sanitized
    return this.sanitizer.bypassSecurityTrustResourceUrl(url);
  }

  /**
   * Sorts the table data by the specified column
   * @param column Column name to sort by
   */
  sortData(column: string): void {
    // If clicking the same column, cycle through: asc -> desc -> default (no sort)
    if (this.sortColumn === column) {
      if (this.sortDirection === 'asc') {
        this.sortDirection = 'desc';
      } else if (this.sortDirection === 'desc') {
        // Third click - reset to default order
        this.sortColumn = null;
        this.sortDirection = 'asc';
        
        // Reset to original data order using our stored copy
        if (this.originalTableData && this.originalTableData.length > 0) {
          this.tableData = JSON.parse(JSON.stringify(this.originalTableData));
        }
        return;
      }
    } else {
      // New column, default to ascending
      this.sortColumn = column;
      this.sortDirection = 'asc';
    }

    // Create a copy of the data to sort
    const data = [...this.tableData];
    
    // Sort the data
    data.sort((a, b) => {
      const valueA = a[column] !== undefined && a[column] !== null ? a[column].toString().toLowerCase() : '';
      const valueB = b[column] !== undefined && b[column] !== null ? b[column].toString().toLowerCase() : '';
      
      // Handle numeric values
      if (!isNaN(valueA) && !isNaN(valueB)) {
        return this.sortDirection === 'asc' 
          ? Number(valueA) - Number(valueB) 
          : Number(valueB) - Number(valueA);
      }
      
      // Handle string values
      if (this.sortDirection === 'asc') {
        return valueA.localeCompare(valueB);
      } else {
        return valueB.localeCompare(valueA);
      }
    });
    
    // Update the table data with the sorted data
    this.tableData = data;
  }
}
