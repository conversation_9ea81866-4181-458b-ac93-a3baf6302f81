export const ADMIN_PAGES_MENU = [
  {
    path: "dashboard",
    data: {
      menu: {
        title: " Dashboard",
        icon: "fa fa-qrcode ng-star-inserted",
        image:'../../assets/img/dashboard.svg',
        selected: false,
        expanded: false,
        order: 0,
      },
    },
  },
  // {
  //   path: 'calibration',
  //   data: {
  //     menu: {
  //       title: ' Calibration',
  //       icon: 'fa fa-crosshairs',
  //       image:'../../assets/img/calibration.svg',
  //       selected: false,
  //       expanded: false,
  //       order: 0,
  //     },
  //   },
  // },
  {
    path: "users",
    data: {
      menu: {
        title: "User Management",
        icon: "fa fa-users",
        image:'../../assets/img/manage_accounts.svg',
        selected: false,
        expanded: false,
        order: 0,
      },
    },
  },  

  // {
  //   path: "zones",
  //   data: {
  //     menu: {
  //       title: " Zones",
  //       icon: "fa fa-globe",
  //       selected: false,
  //       expanded: false,
  //       order: 0,
  //     },
  //   },
  // },
  // {
  //   path: "regions",
  //   data: {
  //     menu: {
  //       title: " Regions",
  //       icon: "fa fa-building-o",
  //       selected: false,
  //       expanded: false,
  //       order: 0,
  //     },
  //   },
  // },
  // {
  //   path: "territories",
  //   data: {
  //     menu: {
  //       title: " Territories",
  //       icon: "fa fa-map",
  //       selected: false,
  //       expanded: false,
  //       order: 0,
  //     },
  //   },
  // },
  {
    path: "product-catalog",
    data: {
      menu: {
        title: " Product Catalog",
        icon: ' fa fa-trophy',
        image:'../../assets/img/product_catalog.svg',
        selected: false,
        expanded: false,
        order: 0,
      },
    },
  },
  {
    path: 'approver-management',
    data: {
      menu: {
        title: 'Create Approval Flow',
        icon: 'fas fa-headset',
        image:'../../assets/img/person_check.svg',
        selected: false,
        expanded: false,
        order: 0,
      },
    },
  },
  {
    path: '',
    data: {
      menu: {
        title: 'Target Ledger',
        icon: 'fa fa-trophy',
        image:'../../assets/img/target.svg',
        selected: false,
        expanded: false,
        order: 0,
      },
    },
      children : [
      // {
      //   path: 'invoices',
      //   data: {
      //     menu: {
      //       title: ' Forages',
      //       icon: 'fa fa-trophy',
      //       image:'../../../../../../assets/img/rewardSchemeIcon.png',
      //       selected: false,
      //       expanded: false,
      //       order: 0,
      //     },
      //   }
      // },  
       {
        path: 'target-management',
        data: {
          menu: {
            title: ' Target Management',
            icon: 'fa fa-history',
            image:'../../../assets/img/target-management.svg',
            selected: false,
            expanded: false,
            order: 0,
          },

        },
      },
      {
        path: 'invoices',
        data: {
          menu: {
            title: 'Invoices',
            icon: 'fa fa-history',
            image: '../../../assets/img/invoice.svg',
            selected: false,
            expanded: false,
            order: 1,
          },
        },
      }
      
    ]
  },
  {
    path: 'supports',
    data: {
      menu: {
        title: 'Support',
        icon: 'fas fa-headset',
        image:'../../assets/img/support.svg',
        selected: false,
        expanded: false,
        order: 0,
      },
    },
  },
  // {
  //   path: 'notifications',
  //   data: {
  //     menu: {
  //       title: 'Notifications',
  //       icon: 'fas fa-headset',
  //       image:'../../assets/img/supportIcon.svg',
  //       selected: false,
  //       expanded: false,
  //       order: 0,
  //     },
  //   },
  // },
];

export const TM_PAGES_MENU_AF = [
  {
    path: "dashboard",
    data: {
      menu: {
        title: " Dashboard",
        icon: "fa fa-dashboard",
        selected: false,
        expanded: false,
        order: 0,
      },
    },
  },

  {
    path: "target-management",
    data: {
      menu: {
        title: " Reward Points History",
        icon: "fa fa-hourglass-half",
        selected: false,
        expanded: false,
        order: 0,
      },
    },
  },
];

export const TM_PAGES_MENU_SWAL = [
  {
    path: "territories",
    data: {
      menu: {
        title: " Territories",
        icon: "fa fa-map",
        selected: false,
        expanded: false,
        order: 0,
      },
    },
  },

  {
    path: "target-management",
    data: {
      menu: {
        title: " Reward Points History",
        icon: "fa fa-hourglass-half",
        selected: false,
        expanded: false,
        order: 0,
      },
    },
  },
];

export const SBO_PAGES_MENU = [
  {
    path: "zones",
    data: {
      menu: {
        title: " Zones",
        icon: "fa fa-globe",
        selected: false,
        expanded: false,
        order: 0,
      },
    },
  },
  {
    path: "regions",
    data: {
      menu: {
        title: " Regions",
        icon: "fa fa-building-o",
        selected: false,
        expanded: false,
        order: 0,
      },
    },
  },
  {
    path: "territories",
    data: {
      menu: {
        title: " Territories",
        icon: "fa fa-map",
        selected: false,
        expanded: false,
        order: 0,
      },
    },
  },

  {
    path: "target-management",
    data: {
      menu: {
        title: " Reward Points History",
        icon: "fa fa-hourglass-half",
        selected: false,
        expanded: false,
        order: 0,
      },
    },
  },
];

export const ZM_PAGES_MENU_AF = [
  {
    path: "dashboard",
    data: {
      menu: {
        title: " Dashboard",
        icon: "fa fa-dashboard",
        selected: false,
        expanded: false,
        order: 0,
      },
    },
  },

  {
    path: "regions",
    data: {
      menu: {
        title: " Regions",
        icon: "fa fa-building-o",
        selected: false,
        expanded: false,
        order: 0,
      },
    },
  },
  {
    path: "territories",
    data: {
      menu: {
        title: " Territories",
        icon: "fa fa-map",
        selected: false,
        expanded: false,
        order: 0,
      },
    },
  },

  {
    path: "target-management",
    data: {
      menu: {
        title: " Reward Points History",
        icon: "fa fa-hourglass-half",
        selected: false,
        expanded: false,
        order: 0,
      },
    },
  },
];

export const ZM_PAGES_MENU_SWAL = [
  {
    path: "zones",
    data: {
      menu: {
        title: " Zones",
        icon: "fa fa-globe",
        selected: false,
        expanded: false,
        order: 0,
      },
    },
  },
  {
    path: "regions",
    data: {
      menu: {
        title: " Regions",
        icon: "fa fa-building-o",
        selected: false,
        expanded: false,
        order: 0,
      },
    },
  },
  {
    path: "territories",
    data: {
      menu: {
        title: " Territories",
        icon: "fa fa-map",
        selected: false,
        expanded: false,
        order: 0,
      },
    },
  },

  {
    path: "target-management",
    data: {
      menu: {
        title: " Reward Points History",
        icon: "fa fa-hourglass-half",
        selected: false,
        expanded: false,
        order: 0,
      },
    },
  },
];

export const RM_PAGES_MENU = [
  {
    path: "dashboard",
    data: {
      menu: {
        title: " Dashboard",
        icon: "fa fa-dashboard",
        selected: false,
        expanded: false,
        order: 0,
      },
    },
  },

  {
    path: "territories",
    data: {
      menu: {
        title: " Territories",
        icon: "fa fa-map",
        selected: false,
        expanded: false,
        order: 0,
      },
    },
  },

  {
    path: "target-management",
    data: {
      menu: {
        title: " Reward Points History",
        icon: "fa fa-hourglass-half",
        selected: false,
        expanded: false,
        order: 0,
      },
    },
  },
];
