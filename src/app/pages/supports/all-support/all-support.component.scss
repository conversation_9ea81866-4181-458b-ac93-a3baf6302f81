@import "../../../theme/sass/_auth";
@import "../../../../styles.scss";
@import "../../../theme/sass/mixins";
@import "../../theme/sass/conf/variables.scss";

// Essential mixins and variables
@include scrollbars(.5em, #d9d9d9, rgba(0, 0, 0, 0));
$font-size: 13px;

// Core utility classes
.width-100 { width: 100%; }
.width-60 { width: 60%; }
.width-40 { width: 40%; }

// Error message styling
.error-message {
  .help-block { color: red; }
}

// Button styling
.submit-color { background-color: #FF8033; }

// Main container structure
.support-container {
  width: 100%;
  overflow-y: hidden;
  padding-left: 14px;
  margin-right: 14px;

  .support-grid-container {
    width: 98%;
    float: left;
    border-radius: $border-radius;
    margin-top: 60px;
    position: relative;

    .support-grid-data-parent-container {
      min-height: 485px;
      background: #ffffff;
      overflow-y: auto;
    }

    .support-grid-data-container {
      width: 100%;
      overflow-y: auto;
      background: #fff;
      border-radius: 2px;
      padding: 10px 15px 25px;

      .area-filter-container {
        display: flex;
        height: 65px;
        .left-column {
          width: 30%;

          .main-campaign {
            padding-top: 18px;

            .panel {
              .panel-body {
                .wizard {
                  min-width: 100%;
                  float: left;
                  background: #fff;
                  margin: 0;
                  margin-top: -18px;
                  font-size: 14px;

                  @media screen and (max-width: 500px) {
                    font-size: 8px;
                  }

                  @media screen and (max-width: 768px) {
                    font-size: 12px;
                  }

                  .profile-tab {
                    float: left;
                    text-align: center;
                    border-bottom: 3px solid #c6c6c6;
                    height: 50px;
                    line-height: 50px;
                    text-decoration: none;
                    cursor: pointer;
                    font-size: 14px;
                    color: #c6c6c6;
                    width: 33%;
                    font-weight: 500;
                    &:last-child { border-right: none; }

                    i { margin-left: 5px; }
                    img {
                      height: 20px;
                      margin-left: 5px;
                    }
                  }

                  .active {
                    border-bottom: 3px solid $button-color;
                    color: #FF8033;
                    
                    img {
                      height: 24px;
                      margin-left: 5px;
                    }
                  }
                }
              }
            }
          }
        }

        // Right column with search and filters
        .right-column {
          padding-top: 15px;
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: flex-end;

          .search-container {
            display: flex;
            align-items: center;
            width: 100%;
            justify-content: flex-end;

            // Button styling
            .export-button button, .add-button button {
              width: 38px;
              height: 38px;
              border-radius: 0.25rem;
              border: 1px solid #FF8033;
              background: #FF8033;
              color: #fff;
            }

            .input-group {
              display: flex;
              justify-content: flex-end;
              margin-bottom: 10px;
              // Search input styling
              .search-input {
                display: flex;
                align-items: center;
                width: 35%;

                .input-group-add {
                  padding: 0.55rem 0.75rem;
                  margin-bottom: 0;
                  font-size: 0.9rem;
                  font-weight: 400;
                  line-height: 1;
                  color: #464a4c;
                  text-align: center;
                  background-color: #fff;
                  border: 1px solid #ccc;
                  border-radius: 0.25rem;
                  width: 85%;
                  margin-left: 11%;
                  display: flex;

                  img {
                    height: 18px;
                    cursor: pointer;
                  }

                  i {
                    float: left;
                    margin-top: 2px;
                  }

                  input {
                    border: none;
                    width: 85%;
                    outline: none;
                  }
                }
              }

              // Category dropdown styling
              .category {
                width: 28%;
                margin: 10px 10px;
                
                angular2-multiselect {
                  width: 100%;
                  
                  ::ng-deep .c-btn {
                    border: 1px solid #FF8033 !important;
                    border-radius: 4px !important;
                    min-height: 36px !important;
                  }
                }
              }

              // Date picker styling
              .date-picker-container {
                width: 30%;
                height: 52px;
                
                mat-form-field {
                  width: 92%;
                  margin: 10px 0px 0px 12px;
                  
                  ::ng-deep .mat-form-field-flex {
                    border: 1px solid #FF8033 !important;
                    border-radius: 4px !important;
                    height: 42px !important;
                  }
                  
                  ::ng-deep .mat-form-field-underline {
                    display: none !important;
                  }
                }
                
                .cancel-button {
                  border: 2px solid #FF8033;
                  background-color: white;
                  color: #FF8033;
                  padding: 5px 15px;
                  border-radius: 4px;
                  font-weight: 500;
                  cursor: pointer;
                }
                
                .submit-button {
                  color: #fff;
                  background-color: #FF8033;
                  border: 2px solid #FF8033;
                  padding: 5px 15px;
                  border-radius: 4px;
                  font-weight: 500;
                  cursor: pointer;
                }
                
                ::ng-deep .mat-datepicker-toggle {
                  color: #666;
                  
                  .mat-icon-button {
                    width: 40px !important;
                    height: 40px !important;
                    position: relative !important;
                    top: 10px;
                  }
                }
                
                ::ng-deep .mat-calendar-body-selected {
                  background-color: #FF8033 !important;
                }
              }

              .export-button, .add-button {
                margin-right: 10px;
                margin-top: 10px;
              }
            }
          }

          // Tablet responsiveness
          @media screen and (max-width: 768px) {
            .search-container {
              justify-content: center;
            }
          }
        }

        // Table styling
        .support-table {
          font-size: 15px;
          min-width: 100%;
          overflow-y: hidden;
        }
      }
    }
  }

  // Toggle button styling
  .mat-button-toggle-group {
    display: inline-flex;
    flex-direction: row;
    border-radius: 2px;
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    outline: none;
    flex-wrap: wrap;
    margin: 0;
  }

  .toggle-group .mat-button-toggle {
    vertical-align: middle !important;
    height: 50px;
    margin: 0;
    padding: 0;
  }

  .mat-button-toggle {
    color: black;
    border: 1px #666666;
    font-weight: 600;
    height: 0;
    width: 250px;
  }

  .mat-button-toggle-checked {
    background-color: #FF8033 !important;
    color: white !important;
    height: 0;
    width: 250px !important;
  }
}

// Support dialog styling
.main-support-container {
  padding: 20px;

  .heading-container {
    color: #FF8033;
    font-family: $sans-font-family;

    h3 { font-weight: 800; }
  }

  // Form input styling
  .form-input1-container {
    display: grid;
    grid-template-columns: repeat(3, 34%);
    gap: 10px;
    width: 97%;
    outline: none;
    padding-bottom: 10px;

    span {
      .input-field {
        font-weight: 400;
        line-height: 1;
        color: #464a4c;
        padding-left: 10px;
        background-color: #fff;
        border: 1px solid #ccc !important;
        border-radius: 0.25rem;
        height: 38px;
        width: 100%;
      }

      input:focus, input:focus-visible {
        outline: none;
      }
    }
  }

  // Textarea styling
  .mat-form {
    textarea {
      width: 100%;
      height: 80px;
      resize: none;
      border: 1px solid #ccc;
      border-radius: 3px;
      padding-left: 10px;
    }

    .error-message { color: red; }
    
    .help-block {
      color: #fd0d0d;
      vertical-align: sub;
      margin: 0 0 0 40px;
    }
  }

  // Button container styling
  .button-container {
    display: flex;
    justify-content: center;
    gap: 20px;

    button {
      height: 40px;
      width: 30%;
      border: none;
      border-radius: 5px;
    }

    .btn-cancel {
      background-color: #fff;
      color: #FF8033;
      font-family: sans-serif;
      font-weight: 600;
      border: 1px solid #FF8033;
    }

    .btn-submit {
      background-color: #FF8033;
      color: #fff;
      font-family: $sans-font-family;
      font-weight: 600;
    }
  }
}

// Responsive styles
@media screen and (min-width: 200px) and (max-width: 575px) {
  .support-container .support-grid-container {
    .support-grid-action {
      .support-grid-search-container {
        width: 100%;
        .support-grid-search-input {
          width: 100%;
        }
      }
      .support-grid-action-container {
        width: 100%;
        display: flex;
        flex-direction: column;
        padding: 10px;
        .support-grid-action-add {
          width: 60%;
          margin-top: 5px;
        }
      }
    }
  }
}

// Material styling overrides
:host ::ng-deep {
  .mat-button-toggle-label-content {
    user-select: none;
    display: inline-block;
    padding: 0 16px;
    width: 245px;
    height: 50px;
    text-align: center;
    border: none;
  }

  .mat-date-range-input-container {
    position: relative;
    width: 300px !important;
  }

  .mat-date-range-input {
    display: block;
    width: 80%;
    font-size: 15px;
    margin-left: 6px;
    margin-bottom: 1%;
  }

  .mat-datepicker-toggle {
    display: flex;
    margin-bottom: 22px;
    justify-content: center;
    align-items: center;
    position: relative;
    bottom: 4px;
  }

  .mat-form-field-appearance-outline .mat-form-field-flex {
    padding: 0 8px 0 6px;
    margin-top: -0.25em;
    position: relative;
    height: 43px !important;
  }

  .mat-form-field-type-mat-date-range-input .mat-form-field-infix {
    width: 200px;
    position: relative;
    bottom: 8px;
  }

  .date-picker-container .mat-form-field-appearance-outline .mat-form-field-wrapper {
    padding: 0;
    border-radius: 4px;
  }

  .date-picker-container .mat-form-field-appearance-outline .mat-form-field-flex {
    height: 42px !important;
  }
}

// Pagination styling
:host ::ng-deep {
  #pagination {
    padding: 0;
    display: inline-block;
    margin: 10px 2px;
    border-radius: 25px;
    
    ul {
      margin: 0;
      padding: 0;
      line-height: 1.5;
      
      li {
        height: 35px;
        text-align: center;
        min-width: 35px;
        line-height: 27px;
        border: none;
        border-right: 1px solid #d6d6d6;
        
        &:last-child {
          border-right: none;
        }
        
        a {
          color: #333;
          text-decoration: none;
          
          &:hover {
            color: #FF8033;
          }
        }
      }
      
      .current {
        background-color: #FF8033;
        color: white;
        
        a {
          color: white;
        }
      }
    }
  }
}

// Disabled submit button
.disable-submit {
  opacity: 0.6;
  cursor: not-allowed;
  
  &:hover {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

// Fix for input group layout
.input-group {
  flex-wrap: unset !important;
}

// Fix for date picker container and calendar icon positioning
.date-picker-container {
  width: 30%;
  height: 52px;
  position: relative; // Add position relative to container
  
  mat-form-field {
    width: 92%;
    margin: 7px 0px 0px 12px;
    
    ::ng-deep .mat-form-field-flex {
      border: 1px solid #FF8033 !important;
      border-radius: 4px !important;
      height: 42px !important;
      display: flex !important;
      align-items: center !important;
    }
    
    ::ng-deep .mat-form-field-infix {
      padding: 0 !important;
      border-top: 0 !important;
    }
    
    ::ng-deep .mat-form-field-suffix {
      position: absolute !important;
      right: 0 !important;
      top: 50% !important;
      transform: translateY(-50%) !important;
    }
    
    ::ng-deep .mat-datepicker-toggle {
      position: absolute !important;
      right: 0 !important;
      top: 50% !important;
      transform: translateY(-50%) !important;
      margin: 0 !important;
    }
    
    ::ng-deep .mat-form-field-underline {
      display: none !important;
    }
  }
  
  // Style for date range picker buttons
  .cancel-button {
    border: 2px solid #FF8033;
    background-color: white;
    color: #FF8033;
    padding: 5px 15px;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
  }
  
  .submit-button {
    color: #fff;
    background-color: #FF8033;
    border: 2px solid #FF8033;
    padding: 5px 15px;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
  }
}

// Override Angular Material datepicker toggle styles
:host ::ng-deep {
  .mat-datepicker-toggle {
    display: inline-block !important;
    position: absolute !important;
    right: 0 !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    margin: 0 !important;
    height: 40px !important;
    width: 40px !important;
    
    .mat-icon-button {
      height: 40px !important;
      width: 40px !important;
      line-height: 40px !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
    }
  }
  
  .mat-form-field-appearance-outline .mat-form-field-flex {
    padding: 0 8px 0 6px !important;
    margin-top: 0 !important;
    position: relative !important;
    height: 42px !important;
    display: flex !important;
    align-items: center !important;
  }
  
  .mat-form-field-type-mat-date-range-input .mat-form-field-infix {
    width: 200px !important;
    padding: 0 !important;
    border-top: 0 !important;
    position: static !important;
  }
  
  .mat-date-range-input-container {
    display: flex !important;
    align-items: center !important;
    position: relative !important;
    width: 100% !important;
    max-width: 300px !important;
  }
  
  .mat-date-range-input {
    display: block !important;
    width: 100% !important;
    font-size: 15px !important;
    margin: 0 !important;
    padding: 0 !important;
  }
}

.disabled-field {
  opacity: 0.6;
  pointer-events: none;
}
