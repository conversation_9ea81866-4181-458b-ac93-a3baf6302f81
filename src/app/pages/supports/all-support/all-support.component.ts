import { Component, OnInit, TemplateRef, ViewChild, ViewEncapsulation } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { PageEvent } from '@angular/material/paginator';
import { Router, RouterModule } from '@angular/router';
import moment from 'moment';
import { ngxCsv } from 'ngx-csv';
import { ToastrService } from 'ngx-toastr';
import { Observable, Subject, debounceTime } from 'rxjs';
import { RewardPointsService } from 'src/app/app-services/reward-points-service';
import { SidebarServiceService } from 'src/app/app-services/sidebar-service.service';
import { SupportService } from 'src/app/app-services/support.service';
import { AppConstant } from 'src/app/constants/app.constant';
import { GlobalEvents } from 'src/app/helpers/global.events';
import { Utility } from "src/app/shared/utility/utility";
import { BaThemeSpinner } from 'src/app/theme/services';
// import * as saveAs from 'file-saver';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
// import { AllSupportRoutingModule } from './all-support-routing.module';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
// import { AllSupportComponent } from './all-support.component';
import { DynamicTableComponent } from 'src/app/shared/data-table/data-table.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AngularMultiSelectModule } from 'angular2-multiselect-dropdown';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatPaginatorModule } from '@angular/material/paginator';
import { CustomCasePipe } from "../../../app-services/custom-case.pipe";
import { UserService } from 'src/app/app-services/user-service';
import { AuthenticationHelper } from 'src/app/helpers/authentication';

interface ConfigurationSettings {
  showPagination: boolean;
  perPage: number;
  totalRecordCount: number;
  currentPage: number;
  showActionsColumn: boolean;
  actionsColumnName: string;
  productIcon: boolean;
  noDataMessage: string;
  showEdit: boolean;
  changeStatus: boolean;
}

export enum ToggleEnum {
  Option1,
  Option2,
}
@Component({
  selector: 'app-all-support',
  templateUrl: './all-support.component.html',
  styleUrls: ['./all-support.component.scss'],
  encapsulation: ViewEncapsulation.None, // This is crucial for production builds
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    ReactiveFormsModule,
    MatButtonToggleModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatButtonModule,
    MatSelectModule,
    MatInputModule,
    MatIconModule,
    MatPaginatorModule,
    MatDialogModule,
    AngularMultiSelectModule,
    DynamicTableComponent,
    CustomCasePipe
]
})

export class AllSupportComponent implements OnInit {
  @ViewChild('supoortDialog') supoortDialog: TemplateRef<any> | undefined;
  supoortDialogRef!: MatDialogRef<any>;

  newImage: string = '../../../../assets/img/new-support.svg';
  disableNewIcon: string = '../../../../assets/img/new-support-disable.svg';
  disableResolvedIcon: string = '../../../../assets/img/resolved-disable.svg';
  resolvedImage: string = '../../../../assets/img/resolved-support.svg';
  ismobileViewAllBrands: boolean = false;
  formGroup: FormGroup = new FormGroup({
    comments: new FormControl('', Validators.required)
  });
  
  dataTable: boolean = false;
  isView: boolean = false;
  isViewFalse: boolean = false;
  newFlag: boolean = false;
  resolvedFlag: boolean = false;
  userData: any = [];
  tableHead: any = [];
  tableColName: any = [];
  tableData: any = [];
  exportData: any = [];
  modelChanged: Subject<string> = new Subject<string>();
  showIndex: any = { index: null };
  
  configurationSettings: ConfigurationSettings = {
    showPagination: true,
    perPage: AppConstant.PER_PAGE_ITEMS,
    totalRecordCount: 0,
    currentPage: 1,
    showActionsColumn: true,
    actionsColumnName: 'Action',
    productIcon: true,
    noDataMessage: 'No data found',
    // showStatus: false,
    showEdit: true,
    changeStatus: false,
    // showIndex: true,
  };

  supportTab: string = 'PROCESSING';
  selectedStatus: string = '';
  activeButton: string | undefined;
  selectedDate: string | undefined;
  selectedValue: string = '1';
  isNewTicket: boolean = true;
  isResolvedTicket: boolean = false;
  model: string = '';
  searchedValue: string | undefined;
  startDate: any;
  endDate: any;

  ticketNumber: string | undefined;
  queryTitle: string | undefined;
  createdDate: Date | undefined;

  totalRecordCount = 0;
  perPage = 20;
  currentPage = 0;

  categoryDataList: any = [];
  dialogCategoryDataList: any = [];
  category: any = [];
  dialogCategory: any = [];
  statusDataList: any = [];
  status: any = [];
  ticketId: number | undefined;

  selectedCategory: string = '';
  selectedDialogCategory: string = '';

  categoryDropdownSettings = {
    text: 'Select Category',
    enableSearchFilter: true,
    classes: 'myclass customer-dd ',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 200,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };

  dialogCategoryDropdownSettings = {
    text: 'Select Category',
    enableSearchFilter: true,
    classes: 'myclass customer-dd ',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 200,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };

  statusDropdownSettings = {
    text: 'Select Status',
    enableSearchFilter: true,
    classes: 'myclass customer-dd ',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 200,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };

  supportAttachments: any = [];
  userRole: string | null | undefined;

  constructor(
    private spinner: BaThemeSpinner, 
    private supportService: SupportService,
    private toastr: ToastrService,
    private router: Router,
    private events: GlobalEvents,
    private utility: Utility,
    public dialog: MatDialog,
    private userService: UserService,
    private rewardPointService: RewardPointsService,
    private sidebarService: SidebarServiceService,
    private formBuilder: FormBuilder
  ) {
    this.ismobileViewAllBrands = window.innerWidth <= 1023 ? true : false;
  }

  ngOnInit() {
    const localRole = AuthenticationHelper.getRole()?.trim().toUpperCase();
    if (localRole) {
      this.setRoleBasedConfig(localRole);
      this.spinner.hide();
    }
    this.userService.userRole$.subscribe(apiRole => {
      const apiRoleFormatted = apiRole?.trim().toUpperCase();
      this.userRole = apiRoleFormatted;
      if (apiRoleFormatted && apiRoleFormatted !== localRole) {
        this.setRoleBasedConfig(apiRoleFormatted);
        this.spinner.hide();
      }
  });
    this.getAllCategoryDropdown();
    this.getAllDialogCategoryDropdown();
    this.getAllStatusDropdown();
    this.setTableHeader();
    this.modelChanged.pipe(debounceTime(500)).subscribe((model: string) => {
      this.searchedValue = model?.trim() || '';
      this.setTableHeader();
    });    
  }
  private setRoleBasedConfig(role: string) {
    if (role === 'VIEWER') {
      this.configurationSettings = {
        ...this.configurationSettings,
        showActionsColumn: false
      };
      setTimeout(() => {
        this.configurationSettings = {...this.configurationSettings};
        this.tableData = [...(this.tableData || [])];
        this.setTableHeader();
      }, 100);
    } 
    else {
      this.configurationSettings = {
        ...this.configurationSettings,
        showActionsColumn: true
      };
    }
  }


  toggleEnum = ToggleEnum;
  selectedState = ToggleEnum.Option1;
  onChange($event: any) {
    this.selectedState = $event.value;
  }
  
  onPageChange(event: PageEvent) {
    this.currentPage = event.pageIndex;
    this.perPage = event.pageSize;
    if (this.isNewTicket) {
      this.processingTickets();
    } else if (this.isResolvedTicket) {
      this.resolvedTickets();
    }
  }
  /**
   * Method for routing to edit or add user
   * @param event
   */
  id: any;
  title: any;
  description: string | undefined;

  userDetails(event: any) {
    let data = {
      id: event.id,
      ticketNumber: event.ticketNumber,
      title: event.title,
      description: event.description,
      comment: event.comment,
    };
    this.supoortDialogRef = this.dialog.open(this.supoortDialog!, {
      width: '70%',
      disableClose: false,
      panelClass: 'confirm-dialog-container',
      data,
      hasBackdrop: true,
    });    
  }

  async openDialogForm(data: any) {
    this.ticketId = data.id;
    await this.waitForTicketInfo(data);
    // this.rewardPointService.emitDisabledSidebar(true);
    // this.rewardPointService.emitOpenedPopup(true);
  
    if (this.supoortDialog) {
      this.supoortDialogRef = this.dialog.open(this.supoortDialog, {
        width: '50%',
        disableClose: false,
        panelClass: 'confirm-dialog-container',
        data: data,
        hasBackdrop: true,
        autoFocus: false,
      });
  
      this.supoortDialogRef.afterClosed().subscribe((result: any) => {
        // this.rewardPointService.emitDisabledSidebar(false);
        // this.rewardPointService.emitOpenedPopup(false);
      });
    } else {
      console.warn('Support dialog template is not available.');
    }
  }  
  
  

  async waitForTicketInfo(data: any): Promise<void> {
    return new Promise<void>((resolve) => {
      this.setTicketInfo(data);
      resolve(); // Resolve the promise immediately since setTicketInfo doesn't return a promise
    });
  }

  setTicketInfo(ticketInfo: any) {
    this.supportAttachments = [];
    ticketInfo.supportAttachments.forEach((supportAttachment: any) => {
      let supportAttachmentObject = {
        id: supportAttachment.id,
        url: supportAttachment.url,
        fileName: supportAttachment.fileName,
        contentType: supportAttachment.contentType,
        fileSize: supportAttachment.fileSize,
        supportMediaType: supportAttachment.supportMediaType,
      };
      this.supportAttachments.push(supportAttachmentObject);
    });
    this.dialogCategory = [];
    this.status = [];
    this.dialogCategoryDataList.forEach((categoryData: any) => {
      if (categoryData.name == ticketInfo.category) {
        this.dialogCategory.push(categoryData);
        this.selectedDialogCategory = categoryData.name;
      }
    });
    this.statusDataList.forEach((statusData: any) => {
      if (statusData.status == ticketInfo.status) {
        this.status.push(statusData);
        this.selectedStatus = statusData.status;
      }
    });
    this.ticketId = ticketInfo.id;
    if (this.isNewTicket) {
      this.formGroup = this.formBuilder.group({
        ticketNumber: [ticketInfo.ticketNumber],
        queryTitle: [ticketInfo.title],
        leaderName: [ticketInfo.leaderName, Validators.required],
        createdDate: [new Date(ticketInfo.createdDate)],
        comments: [ticketInfo.comment, Validators.required],
        description: [ticketInfo.description, Validators.required],
      });
    } else if (this.isResolvedTicket) {
      this.formGroup = this.formBuilder.group({
        ticketNumber: [ticketInfo.ticketNumber, Validators.required],
        queryTitle: [ticketInfo.title, Validators.required],
        leaderName: [ticketInfo.leaderName, Validators.required],
        createdDate: [new Date(ticketInfo.updatedDate), Validators.required],
        comments: [ticketInfo.comment, Validators.required],
        description: [ticketInfo.description, Validators.required],
      });
    } else {
      this.formGroup = this.formBuilder.group({
        ticketNumber: [ticketInfo.ticketNumber, Validators.required],
        queryTitle: [ticketInfo.title, Validators.required],
        leaderName: [ticketInfo.leaderName, Validators.required],
        createdDate: [new Date(ticketInfo.createdDate), Validators.required],
        comments: [ticketInfo.comment, Validators.required],
        description: [ticketInfo.description, Validators.required],
      });
    }
    this.formGroup.patchValue({
      comments: ''
    });
    this.formGroup.get('ticketNumber')?.disable();
    this.formGroup.get('queryTitle')?.disable();
    this.formGroup.get('description')?.disable();
  }

  /**
   * Triggered when category are selected
   * @param data
   */
  onCloseForm() {
    this.supoortDialogRef.close();
    this.formGroup.reset();
    this.selectedCategory = '';
    this.selectedDialogCategory = '';
    this.selectedStatus = '';
    this.categoryDataList = [];
    this.statusDataList = [];
    this.category = [];
    this.status = [];
    this.supportAttachments = [];
    this.formGroup.patchValue({
      comments: '',
    });
    this.getAllCategoryDropdown();
    this.getAllStatusDropdown();
  }

  ChangeStatus(event: any) {}

  /**
   * Method for the page change event
   * @param page
   */
  getUsersPageData(page: any) {
    // this.currentPage = page;
    if (!this.newFlag) {
      this.resolvedTickets(this.currentPage);
    } else {
      this.processingTickets(this.currentPage);
    }
  }
  
  getsupportPageData(page: number): void {
    this.configurationSettings.currentPage = page;
    this.getUsersPageData(this.configurationSettings.currentPage);
  }

  /**
   * Method for setting the headers of the table for different customer types
   */
  setTableHeader() {
    if (this.isNewTicket) {
      this.tableHead = [
        'Sr. No.',
        'Leader Name',
        'Ticket No',
        'Mobile No',
        'Category',
        'Query Title',
        'Created Date',
      ];
      this.tableColName = [
        'Sr_No',
        'leaderName',
        'ticketNumber',
        'mobileNo',
        'category',
        'title',
        'createdDate',
      ];
      this.tableData = [];
      this.processingTickets();
    } else if (this.isResolvedTicket) {
      this.tableHead = [
        'Sr. No.',
        'Leader Name',
        'Ticket No',
        'Mobile No',
        'Category',
        'Query Title',
        'Resolved Date',
      ];
      this.tableColName = [
        'Sr_No',
        'leaderName',
        'ticketNumber',
        'mobileNo',
        'category',
        'title',
        'updatedDate',
      ];
      this.tableData = [];
      this.resolvedTickets();
    } else {
      this.tableHead = ['Leader Name', 'Ticket No', 'Mobile No', 'Category', 'Query Title', 'Created Date'];
      this.tableColName = ['leaderName','ticketNumber', 'mobileNo','category', 'title', 'createdDate'];
      this.tableData = [];
      this.processingTickets();
    }
  }

  onWindowResizeBrands(event: any) {
    this.ismobileViewAllBrands = window.innerWidth <= 1023 ? true : false;
  }

  /**
   * Method for getting the filtered data by the start date and end date
   */
  filterSchemeByDate(start_date: any, end_Date: any) {
    setTimeout(() => {
      if (this.endDate) {
        this.startDate = moment(this.startDate).format('YYYY-MM-DD');
        this.endDate = moment(this.endDate).format('YYYY-MM-DD');
  
        if (this.isNewTicket) {
          this.processingTickets();
        } else if (this.isResolvedTicket) {
          this.resolvedTickets();
        }
      } else {
        this.toastr.warning('Please select end date');
      }
    }, 100);
  }  

  endDateChanged(event: any) {
    setTimeout(() => {
      if (!this.endDate) {
        this.toastr.warning('Please select end date');
      }
    }, 100);
  }  

  /**
   * Method for clear selected date range
   */

  clearDateRange(startInput: any, endInput: any) {
    this.startDate = '';
    this.endDate = '';
    if (this.isNewTicket) {
      this.processingTickets();
    } else if (this.isResolvedTicket) {
      this.resolvedTickets();
    }
  }

  /**
   * Method for getting results on the basis of search query
   * @param searchString
   */
  onSearch(event: any) {
    this.modelChanged.next(event);
  }

  /**
   * Method for clearing search query
   */
  clearSearch() {
    this.searchedValue = '';
    this.model = '';
    this.setTableHeader();
  }

  /**
   * Method for exporting data in CSV format
   * @param event
   */
  onExport(event: any) {
    if (event) {
      this.getSupportExportData();
    }
  }

  /**
   * Triggered when category are selected
   * @param event
   */
  onCategorySelect(event: any) {
    this.selectedCategory = event.name;
    this.selectedDialogCategory = event.name;
    if (this.isNewTicket) {
      this.processingTickets();
    } else {
      this.resolvedTickets();
    }
  }

  /**
   * Triggered when category are selected
   * @param event
   */
  onCategoryDeselect(event: any) {
    this.selectedCategory = '';
    this.selectedDialogCategory = '';
    if (this.isNewTicket) {
      this.processingTickets();
    } else {
      this.resolvedTickets();
    }
  }

  /**
   * Triggered when category are selected
   * @param event
   */
  onDeselectAllCategory(event: any) {
    this.selectedCategory = '';
    this.selectedDialogCategory = '';
    if (this.isNewTicket) {
      this.processingTickets();
    } else {
      this.resolvedTickets();
    }
  }

  /**
   * Triggered when category are selected
   * @param event
   */
  onDialogCategorySelect(event: any) {
    this.selectedDialogCategory = event.name;
    if(this.isNewTicket){
      this.processingTickets();
    }else{
      this.resolvedTickets();
    }
  }

  /**
   * Triggered when category are selected
   * @param event
   */
  onDialogCategoryDeselect(event: any) {
    this.selectedDialogCategory = '';
    if(this.isNewTicket){
      this.processingTickets();
    }else{
      this.resolvedTickets();
    }
  }

  /**
   * Triggered when category are selected
   * @param event
   */
  onDialogDeselectAllCategory(event: any) {
    this.selectedDialogCategory = '';
    if(this.isNewTicket){
      this.processingTickets();
    }else{
      this.resolvedTickets();
    }
  }

  /**
   * Triggered when category are selected
   * @param event
   */
  onStatusSelect(event: any) {
    this.selectedStatus = event.status;
  }

  /**
   * Triggered when category are selected
   * @param event
   */
  onStatusDeselect(event: any) {
    this.selectedStatus = '';
  }

  /**
   * Triggered when category are selected
   * @param event
   */
  onDeselectAllStatus(event: any) {
    this.selectedStatus = '';
  }
  ticketTab(tab: string) {
    this.spinner.show();
    const localRole = AuthenticationHelper.getRole()?.trim().toUpperCase();
    this.model = '';
    this.searchedValue = '';
    this.startDate = '';
    this.endDate = '';
    this.category = '';
    this.selectedCategory = '';

    switch (tab) {
      case 'PROCESSING':
        this.supportTab = 'PROCESSING';
        this.isNewTicket = true;
        this.isResolvedTicket = false;
        if (localRole === 'VIEWER') {
          this.configurationSettings.showActionsColumn = false;
        } else {
          this.configurationSettings.showActionsColumn = true;
        }
        break;

      case 'RESOLVED':
        this.supportTab = 'RESOLVED';
        this.isNewTicket = false;
        this.isResolvedTicket = true;
        this.configurationSettings.showActionsColumn = false;  // Always false for RESOLVED tab
        break;

      default:
        this.isNewTicket = true;
        this.isResolvedTicket = false;
        if (localRole === 'VIEWER') {
          this.configurationSettings.showActionsColumn = false;
        } else {
          this.configurationSettings.showActionsColumn = true;
        }
        break;
    }
    this.configurationSettings = {...this.configurationSettings};
    
    this.setTableHeader();
    this.spinner.hide();
  }
  
  
  

  /**
   * API call to get category dropdown data
   * @param page
   */
  getAllCategoryDropdown() {
    this.supportService.getAllCategoryData().subscribe({
      next: (category: any) => {
        try {
          // First, check if response is a string and parse it if needed
          let parsedResponse = category;
          if (typeof category === 'string') {
            parsedResponse = JSON.parse(category);
          }
          
          // Check if response has encryptedBody property
          if (parsedResponse && parsedResponse.encryptedBody) {
            // Decrypt the encryptedBody
            const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
            parsedResponse = JSON.parse(decrypted);
          } else {
            // Try to decrypt the entire response
            const decrypted = this.utility.decrypt(category);
            parsedResponse = JSON.parse(decrypted);
          }
          
          this.categoryDataList = [];
          parsedResponse.forEach((categoryInfo: any) => {
            let categoryObject = {
              id: categoryInfo.id,
              name: categoryInfo.name,
            };
            this.categoryDataList.push(categoryObject);
          });
        } catch (error) {
          console.error('Error parsing category data:', error);
          this.categoryDataList = [];
        }
      },
      error: (errorResponse: any) => {
        try {
          let error = typeof errorResponse.error === 'string' ? 
            JSON.parse(this.utility.decrypt(errorResponse.error)) : 
            errorResponse.error;
          
          if (typeof error === 'string') {
            error = JSON.parse(error);
          }
          
          if ('Full authentication is required to access this resource' == error.message) {
            localStorage.clear();
            this.router.navigate(['']);
            this.toastr.success('Signed Out Successfully');
          } else {
            this.toastr.error(error.message);
          }
        } catch (e) {
          console.error('Error parsing error response:', e);
          this.toastr.error('An error occurred');
        }
      },
    });
  }
  /**
   * API call to get dialog category dropdown data
   * @param page
   */
  getAllDialogCategoryDropdown() {
    this.supportService.getAllCategoryData().subscribe({
      next: (dialogCategory: any) => {
        try {
          // First, check if response is a string and parse it if needed
          let parsedResponse = dialogCategory;
          if (typeof dialogCategory === 'string') {
            parsedResponse = JSON.parse(dialogCategory);
          }
          
          // Check if response has encryptedBody property
          if (parsedResponse && parsedResponse.encryptedBody) {
            // Decrypt the encryptedBody
            const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
            parsedResponse = JSON.parse(decrypted);
          } else {
            // Try to decrypt the entire response if needed
            if (typeof dialogCategory === 'string') {
              const decrypted = this.utility.decrypt(dialogCategory);
              parsedResponse = JSON.parse(decrypted);
            }
          }
          
          this.dialogCategoryDataList = [];
          parsedResponse.forEach((categoryInfo: any) => {
            let categoryObject = {
              id: categoryInfo.id,
              name: categoryInfo.name,
            };
            this.dialogCategoryDataList.push(categoryObject);
          });
        } catch (error) {
          console.error('Error parsing dialog category data:', error);
          this.dialogCategoryDataList = [];
        }
      },
      error: (errorResponse: any) => {
        try {
          let error = typeof errorResponse.error === 'string' ? 
            JSON.parse(this.utility.decrypt(errorResponse.error)) : 
            errorResponse.error;
          
          if (typeof error === 'string') {
            error = JSON.parse(error);
          }
          
          if ('Full authentication is required to access this resource' == error.message) {
            localStorage.clear();
            this.router.navigate(['']);
            this.toastr.success('Signed Out Successfully');
          } else {
            this.toastr.error(error.message);
          }
        } catch (e) {
          console.error('Error parsing error response:', e);
          this.toastr.error('An error occurred');
        }
      },
    });
  }

  /**
   * API call to get status dropdown data on dialog
   * @param page
   */
  getAllStatusDropdown() {  
    this.supportService.getAllStatusData().subscribe({
      next: (status: any) => {
        try {
          // First, check if response is a string and parse it if needed
          let parsedResponse = status;
          if (typeof status === 'string') {
            parsedResponse = JSON.parse(status);
          }
          
          // Check if response has encryptedBody property
          if (parsedResponse && parsedResponse.encryptedBody) {
            // Decrypt the encryptedBody
            const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
            parsedResponse = JSON.parse(decrypted);
          }
          
          this.statusDataList = [];
          parsedResponse.forEach((statusInfo: any, index: number) => {
            const statusObject = {
              id: index,
              name: this.utility.formatString(statusInfo.replace(/_/g, ' ')),
              status: statusInfo,
            };
            this.statusDataList.push(statusObject);
          });
        } catch (error) {
          console.error('Error parsing status data:', error);
          this.statusDataList = [];
        }
      },
      error: (errorResponse: any) => {
        try {
          let error = typeof errorResponse.error === 'string' ? 
            JSON.parse(this.utility.decrypt(errorResponse.error)) : 
            errorResponse.error;
          
          if (typeof error === 'string') {
            error = JSON.parse(error);
          }
          
          if (error && error.message) {
            this.toastr.error(error.message);
          } else {
            this.toastr.error('An error occurred while fetching status data');
          }
        } catch (e) {
          console.error('Error parsing error response:', e);
          this.toastr.error('An error occurred');
        }
      }
    });
  }
  

  /**
   * API call
   * @param page
   */
  processingTickets(page?: any) {
    this.supportService.setButtonState('new');
    this.spinner.show();
    let data = {
      currentPage: this.currentPage ? this.currentPage : 0,
      pageSize: this.perPage,
      searchedValue: this.searchedValue ? this.searchedValue : '',
      category: this.selectedCategory ? this.selectedCategory : '',
      startDate: this.startDate ? this.startDate : '',
      endDate: this.endDate ? this.endDate : '',
      sort: 'updatedDate,desc',
      isProcessing: true,
    };
    this.supportService.getProcessingTickets(data).subscribe({
      next: (processingTicket: any) => {
        try {
          // First, check if response is a string and parse it if needed
          let parsedResponse = processingTicket;
          if (typeof processingTicket === 'string') {
            parsedResponse = JSON.parse(processingTicket);
          }
          
          // Check if response has encryptedBody property
          if (parsedResponse && parsedResponse.encryptedBody) {
            // Decrypt the encryptedBody
            const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
            parsedResponse = JSON.parse(decrypted);
          } else {
            // Try to decrypt the entire response
            const decrypted = this.utility.decrypt(processingTicket);
            parsedResponse = JSON.parse(decrypted);
          }
          
          if (parsedResponse && parsedResponse.content && parsedResponse.content.length) {
            this.tableData = [];
            let i = 1;
            parsedResponse.content.forEach((processingTicketInfo: any) => {
              this.showIndex = i++;
              let processingTicketObj = {
                Sr_No: this.showIndex,
                id: processingTicketInfo.id ? processingTicketInfo.id : 'NA',
                ticketNumber: processingTicketInfo.ticketNumber
                  ? processingTicketInfo.ticketNumber
                  : 'NA',
                leaderName: processingTicketInfo.customerName
                  ? this.utility.toUpperCaseUtil(processingTicketInfo.customerName)
                  : 'NA',
                mobileNo: processingTicketInfo.mobileNo
                  ? processingTicketInfo.mobileNo
                  : 'NA',
                title: processingTicketInfo.title
                  ? processingTicketInfo.title
                  : 'NA',
                description: processingTicketInfo.description
                  ? processingTicketInfo.description
                  : 'NA',
                comment: processingTicketInfo.comment
                  ? processingTicketInfo.comment
                  : '',
                category: processingTicketInfo.category
                  ? processingTicketInfo.category
                  : 'NA',
                createdDate: processingTicketInfo.createdDate
                  ? processingTicketInfo.createdDate
                  : 'NA',
                status: processingTicketInfo.supportTicketStatus
                  ? processingTicketInfo.supportTicketStatus
                  : '',
                supportAttachments: processingTicketInfo.supportAttachments
                  ? processingTicketInfo.supportAttachments
                  : [],
              };
              this.tableData.push(processingTicketObj);
            });
            this.configurationSettings.totalRecordCount = parsedResponse.totalElements;
          } else {
            this.configurationSettings.totalRecordCount = 0;
            this.tableData = [];
            this.newFlag = false;
          }
          localStorage.setItem('newFlag', JSON.stringify(this.newFlag));
          this.events.setChangedContentTopText(
            'Support (' + this.configurationSettings.totalRecordCount + ')'
          );
          this.newFlag = true;
          this.spinner.hide();
        } catch (error) {
          console.error('Error parsing processing tickets:', error);
          this.configurationSettings.totalRecordCount = 0;
          this.tableData = [];
          this.spinner.hide();
        }
      },
      error: (errorResponse: any) => {
        try {
          let error = typeof errorResponse.error === 'string' ? 
            JSON.parse(this.utility.decrypt(errorResponse.error)) : 
            errorResponse.error;
          
          if (typeof error === 'string') {
            error = JSON.parse(error);
          }
          
          if (error && error.message) {
            this.toastr.error(error.message);
          }
          
          let errorMsg = errorResponse.status;
          if (+errorMsg === 401 || +errorMsg === 404) {
            localStorage.clear();
            this.router.navigate(['']);
            this.toastr.success('Signed Out Successfully');
          }
        } catch (e) {
          console.error('Error parsing error response:', e);
          this.toastr.error('An error occurred');
        }
        this.spinner.hide();
      },
    });
  }

  /**
   * API call
   * @param page
   */
  resolvedTickets(page?: any) {
    this.supportService.setButtonState('resolved');
    this.selectedValue = '2';
    this.spinner.show();
    this.newFlag = false;
    let data = {
      currentPage: this.currentPage ? this.currentPage : 0,
      pageSize: this.perPage,
      searchedValue: this.searchedValue ? this.searchedValue : '',
      category: this.selectedCategory ? this.selectedCategory : '',
      startDate: this.startDate ? this.startDate : '',
      endDate: this.endDate ? this.endDate : '',
      sort: 'updatedDate,desc',
      isProcessing: false,
    };
    this.currentPage = 0;
    this.supportService.getResolvedTickets(data).subscribe({
      next: (res: any) => {
        try {
          // First, check if response is a string and parse it if needed
          let parsedResponse = res;
          if (typeof res === 'string') {
            parsedResponse = JSON.parse(res);
          }
          
          // Check if response has encryptedBody property
          if (parsedResponse && parsedResponse.encryptedBody) {
            // Decrypt the encryptedBody
            const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
            parsedResponse = JSON.parse(decrypted);
          } else {
            // Try to decrypt the entire response if needed
            if (typeof res === 'string') {
              const decrypted = this.utility.decrypt(res);
              parsedResponse = JSON.parse(decrypted);
            }
          }
          
          if (parsedResponse && parsedResponse.content && parsedResponse.content.length) {
            this.tableData = [];
            let i = 1;
            parsedResponse.content.forEach((resolvedTicketInfo: any) => {
              this.showIndex = i++;
              let data = {
                Sr_No: this.showIndex,
                id: resolvedTicketInfo.id ? resolvedTicketInfo.id : 'NA',
                ticketNumber: resolvedTicketInfo.ticketNumber
                  ? resolvedTicketInfo.ticketNumber
                  : 'NA',
                leaderName: resolvedTicketInfo.customerName
                  ? this.utility.toUpperCaseUtil(resolvedTicketInfo.customerName)
                  : 'NA',
                mobileNo: resolvedTicketInfo.mobileNo
                  ? resolvedTicketInfo.mobileNo
                  : 'NA',
                title: resolvedTicketInfo.title
                  ? resolvedTicketInfo.title
                  : 'NA',
                description: resolvedTicketInfo.description
                  ? resolvedTicketInfo.description
                  : 'NA',
                comment: resolvedTicketInfo.comment
                  ? resolvedTicketInfo.comment
                  : 'NA',
                category: resolvedTicketInfo.category
                  ? resolvedTicketInfo.category
                  : 'NA',
                updatedDate: resolvedTicketInfo.updatedDate
                  ? resolvedTicketInfo.updatedDate
                  : 'NA',
                status: resolvedTicketInfo.supportTicketStatus
                  ? resolvedTicketInfo.supportTicketStatus
                  : '',
                supportAttachments: resolvedTicketInfo.supportAttachments
                  ? resolvedTicketInfo.supportAttachments
                  : [],
              };
              this.tableData.push(data);
            });
            this.configurationSettings.totalRecordCount = parsedResponse.totalElements;
            this.spinner.hide();
          } else {
            this.configurationSettings.totalRecordCount = 0;
            this.tableData = [];
            this.resolvedFlag = false;
          }
          this.events.setChangedContentTopText(
            'Support (' + this.configurationSettings.totalRecordCount + ')'
          );
          this.resolvedFlag = false;
          this.spinner.hide();
        } catch (error) {
          console.error('Error parsing resolved tickets:', error);
          this.configurationSettings.totalRecordCount = 0;
          this.tableData = [];
          this.spinner.hide();
        }
      },
      error: (errorResponse: any) => {
        try {
          let error = typeof errorResponse.error === 'string' ? 
            JSON.parse(this.utility.decrypt(errorResponse.error)) : 
            errorResponse.error;
          
          if (typeof error === 'string') {
            error = JSON.parse(error);
          }
          
          if (error && error.message) {
            this.toastr.error(error.message);
          }
          
          let errorMsg = errorResponse.status;
          if (+errorMsg === 401 || +errorMsg === 404) {
            localStorage.clear();
            this.router.navigate(['']);
            this.toastr.success('Signed Out Successfully');
          }
        } catch (e) {
          console.error('Error parsing error response:', e);
          this.toastr.error('An error occurred');
        }
        this.spinner.hide();
      },
    });
  }

  /**
   * API call for exporting data in CSV format
   */
  getSupportExportData() {
    window.scrollTo(0, 0);
    this.spinner.show();
    let data = {
      status: this.supportTab == 'PROCESSING' ? true : false,
      currentPage: this.currentPage ? this.currentPage : 0,
      pageSize: this.perPage,
      searchedValue: this.searchedValue ? this.searchedValue : '',
      category: this.selectedCategory ? this.selectedCategory : '',
      startDate: this.startDate ? this.startDate : '',
      endDate: this.endDate ? this.endDate : '',
      sort: 'updatedDate,desc',
      unPaged: true,
    };
    const exports = this.supportService.getAllSupportExportData(data);
    exports.subscribe({
      next: (exportSupportData: any) => {
        try {
          // First, check if response is a string and parse it if needed
          let parsedResponse = exportSupportData;
          if (typeof exportSupportData === 'string') {
            parsedResponse = JSON.parse(exportSupportData);
          }
          
          // Check if response has encryptedBody property
          if (parsedResponse && parsedResponse.encryptedBody) {
            // Decrypt the encryptedBody
            const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
            parsedResponse = JSON.parse(decrypted);
          } else {
            // Try to decrypt the entire response if needed
            if (typeof exportSupportData === 'string') {
              const decrypted = this.utility.decrypt(exportSupportData);
              parsedResponse = JSON.parse(decrypted);
            }
          }
          
          this.exportData = [];
          if (parsedResponse && parsedResponse.content && parsedResponse.content.length) {
            parsedResponse.content.forEach((exportSupportInfo: any) => {
              let exportObj = {
                leaderName: exportSupportInfo.customerName
                  ? this.utility.toUpperCaseUtil(exportSupportInfo.customerName)
                  : 'NA',
                ticketNumber: exportSupportInfo.ticketNumber
                  ? exportSupportInfo.ticketNumber
                  : 'NA',
                mobileNo: exportSupportInfo.mobileNo
                  ? exportSupportInfo.mobileNo
                  : 'NA',
                title: exportSupportInfo.title ? exportSupportInfo.title : 'NA',
                description: exportSupportInfo.description
                  ? exportSupportInfo.description
                  : 'NA',
                comment: exportSupportInfo.comment
                  ? exportSupportInfo.comment
                  : 'NA',
                category: exportSupportInfo.category
                  ? exportSupportInfo.category
                  : 'NA',
              };
              this.exportData.push(exportObj);
            });
            let options = {
              fieldSeparator: ',',
              quoteStrings: '"',
              decimalseparator: '.',
              showLabels: true,
              headers: [
                'Leader Name',
                'Ticket Number',
                'Mobile No',
                'Query Title',
                'Description',
                'Comments',
                'Category',
              ],
            };
            if (this.isNewTicket) {
              new ngxCsv(this.exportData, 'New Ticket', options);
            } else {
              new ngxCsv(this.exportData, 'Resolved Ticket', options);
            }
          } else {
            this.toastr.warning('No data available');
          }
          this.spinner.hide();
        } catch (error) {
          console.error('Error parsing export data:', error);
          this.toastr.warning('No data available');
          this.spinner.hide();
        }
      },
      error: (errorResponse: any) => {
        try {
          let error = typeof errorResponse.error === 'string' ? 
            JSON.parse(this.utility.decrypt(errorResponse.error)) : 
            errorResponse.error;
          
          if (typeof error === 'string') {
            error = JSON.parse(error);
          }
          
          if (error && error.message) {
            this.toastr.error(error.message);
          }
          
          let errorMsg = errorResponse.status;
          if (+errorMsg === 401 || +errorMsg === 404) {
            localStorage.clear();
            this.router.navigate(['']);
            this.toastr.success('Signed Out Successfully');
          }
        } catch (e) {
          console.error('Error parsing error response:', e);
          this.toastr.error('An error occurred');
        }
        this.spinner.hide();
      },
    });
  }

  functionForName(event: any) {
    const k = event.charCode || event.keyCode;
    const lastChar = event.target.value.slice(-1);

    if (event.target.selectionStart === 0 && event.code === 'Space') {
      event.preventDefault();
    }

    if (lastChar === ' ' && event.code === 'Space') {
      event.preventDefault();
    }

    // Allow letters (uppercase and lowercase), numbers, spaces, and specified characters
    if (
      (k >= 65 && k <= 90) ||
      (k >= 97 && k <= 122) ||
      k === 32 || // space
      (k >= 48 && k <= 57) || // numbers
      k === 8 || // backspace
      k === 95 || // underscore
      k === 45 || // hyphen
      k === 58 || // colon
      k === 46 || // period
      k === 44 || // comma
      k === 63 || // question mark
      k === 34 || // double quote
      k === 39 || // single quote
      k === 40 || // open parenthesis
      k === 41 || // close parenthesis
      k === 91 || // open square bracket
      k === 93 || // close square bracket
      k === 38 || // ampersand
      k === 42 // asterisk
    ) {
      return true;
    } else {
      event.preventDefault();
      return false;
    }
  }


  /**
   * Triggered when submit button clicked
   * @param formData
   */
  onSubmit() {
    if (this.formGroup.valid) {
      const formData = this.formGroup.value;
      const dataToSend = {
        ticketId: (JSON.stringify(this.ticketId)),
        status: (this.selectedStatus),
        category: (this.selectedDialogCategory),
        comment: (formData.comments),
      };
      this.spinner.show();
      this.supportService.getUpdateTicket(dataToSend).subscribe({
        next: (ticketResponse: any) => {
          try {
            // First, check if response is a string and parse it if needed
            let parsedResponse = ticketResponse;
            if (typeof ticketResponse === 'string') {
              parsedResponse = JSON.parse(ticketResponse);
            }
            
            // Check if response has encryptedBody property
            if (parsedResponse && parsedResponse.encryptedBody) {
              // Decrypt the encryptedBody
              const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
              parsedResponse = JSON.parse(decrypted);
            } else {
              // Try to decrypt the entire response if needed
              if (typeof ticketResponse === 'string') {
                const decrypted = this.utility.decrypt(ticketResponse);
                parsedResponse = JSON.parse(decrypted);
              }
            }
            
            this.toastr.success(parsedResponse.message);
            this.onCloseForm();
            if (this.isNewTicket) {
              this.isNewTicket = true;
              this.isResolvedTicket = false;
              this.processingTickets();
            } else {
              this.isNewTicket = false;
              this.isResolvedTicket = true;
              this.resolvedTickets();
            }
            this.setTableHeader();
          } catch (error) {
            console.error('Error parsing ticket response:', error);
            this.toastr.error('An error occurred while updating the ticket');
          }
          this.spinner.hide();
        },
        error: (errorResponse: any) => {
          try {
            let error = typeof errorResponse.error === 'string' ? 
              JSON.parse(this.utility.decrypt(errorResponse.error)) : 
              errorResponse.error;
            
            if (typeof error === 'string') {
              error = JSON.parse(error);
            }
            
            if (error && error.message) {
              this.toastr.error(error.message);
            } else {
              this.toastr.error('An error occurred while updating the ticket');
            }
            
            let errorMsg = errorResponse.status;
            if (+errorMsg === 401 || +errorMsg === 404) {
              localStorage.clear();
              this.router.navigate(['']);
              this.toastr.success('Signed Out Successfully');
            }
          } catch (e) {
            console.error('Error parsing error response:', e);
            this.toastr.error('An error occurred');
          }
          this.spinner.hide();
        },
      });
    } else {
      // Mark all form controls as touched to trigger validation messages
      Object.keys(this.formGroup.controls).forEach(key => {
        const control = this.formGroup.get(key);
        control?.markAsTouched();
      });
    }
  }

  showTooltipIfTruncated(event: MouseEvent): void {
    const inputElement = event.target as HTMLInputElement;
    if (inputElement && inputElement.scrollWidth > inputElement.clientWidth) {
      inputElement.title = inputElement.value; 
    } else {
      inputElement.removeAttribute("title"); 
    }
  }    

  funRestSearchPrevent(event: any) {
    if (event.target.selectionStart === 0 && event.code === 'Space') {
      event.preventDefault();
    }
    if (event.target.value.substr(-1) === ' ' && event.code === 'Space') {
      event.preventDefault();
    }

    var k = event.charCode || event.keyCode;
    if (
      (k >= 65 && k <= 90) ||
      (k >= 97 && k <= 122) ||
      k == 8 ||
      k == 32 ||
      (k >= 48 && k <= 57)
    ) {
      // Allow uppercase letters, lowercase letters, backspace, space, and numbers
    } else {
      event.preventDefault();
    }

    return (
      (k >= 65 && k <= 90) ||
      (k >= 97 && k <= 122) ||
      k == 8 ||
      k == 32 ||
      (k >= 48 && k <= 57)
    );
  }

  downloadTemplate() {
    this.spinner.show();
    const exports: Observable<Blob> = this.supportService.downloadTemplate();
    exports.subscribe({
      next: (exportsData: Blob) => {
        this.saveDataAsExcel(
          exportsData,
          'Samridhi_Users_Bulk_Upload_Template'
        );
        this.spinner.hide();
      },
      error: (errorResponse: any) => {
        this.spinner.hide();
      },
    });
  }

  saveDataAsExcel(data: Blob, fileName: string): void {
    const excelData = new Blob([data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });
    saveAs(excelData, fileName + '.xlsx');
  }
  // Method to trigger file download
  downloadImageFile(data: any, filename: string) {
    const blob = new Blob([data], { type: 'image/png' }); // Adjust the type according to your image type
    const url = window.URL.createObjectURL(blob);
    // Create a link element
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    // Append the link to the body
    document.body.appendChild(a);
    // Click the link to start download
    a.click();
    // Cleanup
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  }

  downloadFile(file: any, type: any, fileName: any) {
    this.spinner.show();
    const exports: Observable<Blob> = this.supportService.downloadFile(file);
    exports.subscribe({
      next: (exportsData: Blob) => {
        if(type=='jpg'){
          this.downloadImageFile(
            exportsData,
            fileName
          );
        }else{
          this.downloadVideo(
            exportsData,
            fileName
          );
        }
        this.spinner.hide();
      },
      error: (errorResponse: any) => {
        this.spinner.hide();
      },
    });
  }
  downloadVideo(data: any, filename: string) {
    const blob = new Blob([data], { type: 'video/mp4' }); // Adjust the type according to your video format
    const url = window.URL.createObjectURL(blob);
    // Create a link element
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;

    // Append the link to the body
    document.body.appendChild(a);

    // Click the link to start download
    a.click();

    // Cleanup
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  }
  downloadFileVideo(file: any) {
    this.spinner.show();
    const exports: Observable<Blob> = this.supportService.downloadFile(file);
    exports.subscribe({
      next: (exportsData: Blob) => {
        this.downloadVideo(exportsData, 'video_filename.mp4');
        this.spinner.hide();
      },
      error: (errorResponse: any) => {
        this.spinner.hide();
      },
    });
  }
}
function saveAs(excelData: Blob, arg1: string) {
  throw new Error('Function not implemented.');
}

