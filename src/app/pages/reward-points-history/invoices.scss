@import "../../theme/sass/_auth";
@import "../../../styles";
@import "../../theme/sass/mixins";
@import "../../theme/sass/conf/variables";

$font-size: 13px;

  ::ng-deep {
    .toast-container {
      position: fixed !important;
      z-index: 999999 !important;
      pointer-events: auto !important;
      top: 12px !important;
      right: 12px !important;
      width: auto !important;
      max-width: 350px !important;
      display: block !important;
      visibility: visible !important;

      .ngx-toastr {
        display: flex !important;
        align-items: center;
        gap: 10px;
        padding: 12px 16px;
        border-radius: 6px;
        color: #fff !important;
        font-weight: 500;
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease-in !important;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        word-break: break-word;
        white-space: normal;

        .toast-message {
          flex: 1 1 auto;
          white-space: normal !important;
          word-break: break-word !important;
          overflow: visible !important;
        }

        .toast-progress {
          position: absolute;
          bottom: 0;
          left: 0;
          height: 4px;
          background-color: rgba(255, 255, 255, 0.8);
          animation: progressBar 3s linear forwards;
        }

        &:hover .toast-progress {
          animation-play-state: paused;
        }

        &::before {
          font-size: 18px;
          margin-right: 8px;
          flex-shrink: 0;
          color: #fff !important;
        }

        &.toast-success {
          background-color: #28a745;

          &::before {
            content: '✔';
          }
        }

        &.toast-error {
          background-color: #dc3545;

          &::before {
            content: '✖';
          }
        }

        &.toast-info {
          background-color: #17a2b8;
        }

        &.toast-warning {
          background-color: #ffc107;
          color: #212529;
        }
      }
    }

    @keyframes progressBar {
      from {
        width: 100%;
      }
      to {
        width: 0%;
      }
    }
  }
  ::ng-deep .mdc-icon-button {
    margin-top: -13px !important;
    font-size: 12px !important;
  }

  ::ng-deep .mat-date-range-input-container {
    // margin-top: 2px !important;
        font-size: 13px !important;
        font-family: $sans-font-family;
  }

  .filter-menu-container {
    padding: 0 5 10px 5px;
    .filter-heading {
      margin: 10px 15px;
      font-family: $sans-font-family;
    }

    .date-picker-container {
      width: 100%;
      mat-form-field {
        width: 100%;
      }
    }

    .district-filter-container {
      .agreement-status .cuppa-dropdown .selected-list .c-btn {
        border-radius: 4px;
        padding: 8px;
        border-color: #FF8033;
      }
    }
  
    .filter-container {
      width: 90%;
      margin: 0 14px;
      .selected-list .countplaceholder {
        position: absolute;
        right: 3px;
        top: -34%;
        transform: translateY(-50%);
      }
    }
  
    .button-container {
      display: flex;
      justify-content: center;
      gap: 10px;
      width: 100%;
      padding: 10% 6px 0px 13px;
  
      button {
        height: 35px;
        border: none;
        border-radius: 5px;
        display: flex;
        align-items: center;
      }
  
      .btn-cancel {
        display: flex;

        background-color: #fff;
        color: #FF8033;
        font-family: sans-serif;
        font-weight: 600;
        width: 100%;
        border: 1px solid #FF8033;
        align-items: center;
      }
  
      .btn-submit {
        display: flex;

        background-color: #FF8033;
        color: #fff;
        font-family: $sans-font-family;
        font-weight: 600;
        width: 100%;
      }
  
      .sbt-button-width {
        background-color: #808080;
        color: #fff;
        font-family: $sans-font-family;
        font-weight: 600;
        width: 18%;

      }
    }
  }
  
  :host ::ng-deep .selected-list .c-list .c-token {
    font-size: 11px;
  }
  
  :host ::ng-deep .selected-list .c-btn {
    width: 100%;
  }


  
  .FilterTitleSize {
    font-size: 20px;
    font-family: sans-serif;
    color: #222222;
    font-weight: bold;
  }
  
  .fixed-header {
    position: sticky;
    top: 0;
    z-index: 1;
  }
  
  .mat-menu-item {
    justify-content: center !important;
    align-items: center !important;
    display: flex !important;
    height: 34px !important;
  }
  h3 {
    margin: 2rem 0 1rem;
    font-size: 1.3rem;
    font-weight: 600;
    color: #222;
  }


@import "../../theme/sass/_auth";
@import "../../../styles";
@import "../../theme/sass/mixins";

.upload-row {
  display: flex;
  gap: 20px;
  margin-top: 15px;
  margin-bottom: 10px;
  width: 100% !important;
}

.upload-container {
  padding: 30px;
  text-align: center;
  width: 49%;
  height: 150px;
  border-radius: 7px;
}

.pdf-box {
  border: 2px dashed #ef5350;
  background-color: #ffe9e9;
}

.xml-box {
  border: 2px dashed #5eae00;
  background-color: #f4ffe8;
}

.uploaded-files {
  /* Existing styles */
  padding: 10px;
  width: 30%;
  height: 150px;
  overflow-y: auto; /* Enable scrolling if needed */
  
  .file-name {
    display: flex;
    align-items: center;
    padding: 8px 10px 8px 0;
    // margin-bottom: 8px;
    border-radius: 7px;
    white-space: nowrap; /* Prevent wrapping */
    overflow: hidden; /* Hide overflow */
    

      text-overflow: ellipsis; 
      max-width: 180px; 
      display: inline-block;
    
    
    img {
      flex-shrink: 0; /* Prevent icon from shrinking */
      margin-right: 10px;
    }
  }
  
  .pdf-file-name {
    background-color: #ffe9e9;
  }
  
  .xml-file-name {
    background-color: #f4ffe8;
  }
}

/* File item styles */
.pdf-file-name {
  padding: 8px 10px;
  border-radius: 7px;
  background-color: #ffe9e9;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.xml-file-name {
  padding: 8px 10px;
  border-radius: 7px;
  background-color: #f4ffe8;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.upload-box img {
  width: 40px;
  margin-bottom: 5px;
}

.upload-header{
  font-family: $sans-font-family;
}

.upload-box button {
  width: 120px;
  height: 37px;
  border-radius: 6px;
  background-color: #d9d9d9;
  border: 2px solid #d9d9d9;
  cursor: pointer;
}

/* File icons in list */
.uploaded-files img {
  width: 25px;
  // margin-right: 10px;
  background-color: #fff;
  padding: 5px;
  border-radius: 50%;
}

agm-map {
  height: 100%;
}

.upload-row {
  display: flex;
  gap: 20px;
  margin-top: 15px;
  margin-bottom: 10px;
  width: 100% !important;
}
.upload-container {
  padding: 10px;
  text-align: center;
  width: 49%;
  height: 170px;
  border-radius: 7px;
}

.pdf-box {
  border: 2px dashed #EF5350;
  background-color: #ffe9e9;
}

.xml-box {
  border: 2px dashed #5EAE00;

  background-color: #f4ffe8;
}
.pdf-file-name {
  padding-left: 10px;
  border-radius: 7px;
  background-color: #ffe9e9;
  height: 57px;
}

.pdf-file-name img {
  background-color: #fff;
  padding: 5px;
  border-radius: 50%;
}

.input-field {
  font-weight: 400;
  line-height: 1;
  color: #464a4c;
  padding-left: 10px;
  background-color: #F5F5F5;
  border: none;
  border-radius: 0.25rem;
  height: 38px;
  width: 100%;
}

.table-incentive-date-container {
  margin-right: 10px;
}


.form-group-row input:focus {
    border: none;
  }


.xml-file-name img {
  background-color: #fff;
  padding: 5px;
  border-radius: 50%;
}

.xml-file-name {
  padding-left: 10px;
  border-radius: 7px;
  height: 50px;
  background-color: #f4ffe8;
}
.upload-box img {
  width: 40px;
  margin-bottom: 5px;
}
.filename-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 98%;
  display: block;
}
.icon{
  color: #FF6F1F;
  padding-right: 10px;
}
.delete-icon{
  color: #FF6F1F;
  display: flex;
  justify-content: end;
  font-size: 18px;
  cursor: pointer;
}
.preview-label{
  color: #000000;
}
.form-input1-container {
  display: flex;
  justify-content: space-between;
  gap: 10px;
  margin-bottom: 15px;
  span {
    .input-field {
      font-size: 14px;
      font-weight: 400;
      line-height: 1;
      color: #464a4c;
      padding-left: 10px;
      background-color: #fff;
      border: 1px solid #FF8033;
      border-radius: 0.25rem;
      height: 38px;
      width: 100%;
    }
    .input-field:disabled {
      border-color: #ccc;
      color: #a7a4a4;
    }
  }
  /* Media query for smaller screens, adjust as needed */
  @media screen and (max-width: 768px) {
    .form-input1-container {
      flex-direction: column;
      align-items: center;
    }
  }

  .selected-list .countplaceholder {
    position: absolute;
    right: 50px;
    top: 50%;
    transform: translateY(-100%);
    opacity: 0;
  }
}
.upload-box button {
  width: 100%;
  border-radius: 6px;
  background-color: transparent;
  border: 1px solid #707070;
}
.btn-upload {
  margin-top: 5px;
  padding: 5px 10px;
  cursor: pointer;
  font-family: $sans-font-family;
}
/* Uploaded files container */
.uploaded-files {
  padding: 10px;
  border: 2px solid #ff6f1f;
  border-radius: 7px;
  min-height: 150px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* File display item */
.file-display {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 8px;
  border-radius: 4px;
}

.pdf-file {
  background-color: #ffe9e9;
}

.xml-file {
  background-color: #f4ffe8;
}

// .file-icon {
//   width: 15px;
//   height: 24px;
//   flex-shrink: 0;
// }

.file-info {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

// .file-name {
//   font-size: 14px;
//   white-space: nowrap;
//   overflow: hidden;
//   text-overflow: ellipsis;
//   color: #333;
// }

.file-size {
  font-size: 12px;
  color: #666;
}

.empty-state {
  color: #999;
  text-align: center;
  padding: 20px;
}
.uploaded-files p {
  margin: 5px 0;
  display: flex;
  align-items: center;
}


.app-container {
  width: 100%;
  padding-left: 15px;
  overflow-y: hidden;
  .app-grid-container {
    width: 98%;
    float: left;
    border-radius: $border-radius;
    margin-top: 50px;
    position: relative;
    .app-grid-data-container {
      width: 100%;
      overflow-y: auto;
      background: #fff;
      border-radius: 2px;
      padding: 10px 15px 25px;
      float: left;
      margin: 10px 0;
      .view-switch {
        width: 12%;
        float: right;
        @media screen and (max-width: 768px) {
          width: 100%;
        }
        .map-view {
          width: 30%;
          float: right;
          background: #a4a4a4;
          font-size: 22px;
          text-align: center;
          border-radius: 3px;
          margin: 0 0 0 11px;
          color: #ffffff;
          cursor: pointer;
        }
        .active {
          background: #102d69;
        }
        .disable {
          opacity: 0.9;
          cursor: not-allowed;
        }
      }

      .app-table {
        font-size: 15px;
        width: 100%;
        overflow-y: hidden;
        float: left;
        @media screen and (max-width: 900px) {
          width: 100%;
        }
      }

      .rewards-map {
        width: 50%;
        height: 120vh;
        float: left;
        padding: 15px 0 15px 10px;
        .name {
          line-height: 17px;
          font-size: 14px;
          font-weight: 500;
          color: #333;
        }
        @media screen and (max-width: 900px) {
          width: 100%;
        }
      }
      .no-result {
        width: 100%;
        float: left;
        text-align: center;
        line-height: 43px;
        background-color: rgba(0, 0, 0, 0.1);
        margin-top: 10px;
        font-size: 15px;
        color: #666679;
      }

      .full-width {
        width: 100%;
      }
      .hide {
        display: none;
      }

      .history-filter-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        // margin-bottom: 5px;
        .left-column {
          width: 30%;
          .main-campaign {
            .panel {
              .table-heading {
                color: #FF8033;
              }
              .panel-body {
                .wizard {
                  width: 60%;
                  float: left;
                  background: #fff;
                  margin: 0;
                  margin-bottom: 15px;
                  // margin-top: -22px;
                  font-size: 14px;
                  @media screen and (max-width: 500px) {
                    font-size: 8px;
                  }
                  @media screen and (max-width: 768px) {
                    font-size: 12px;
                  }
                  .profile-tab {
                    float: left;
                    text-decoration: none;
                    text-align: center;
                    border-bottom: 3px solid #c6c6c6;
                    height: 50px;
                    line-height: 50px;
                    cursor: pointer;
                    font-size: 14px;
                    color: #c6c6c6;
                    width: 33%;
                    &:last-child {
                      border-right: none;
                    }

                    i {
                      margin-left: 5px;
                    }
                  }
                  .active {
                    border-bottom: 3px solid $button-color;
                  }
                }
                .wizard .active {
                  border-bottom: 3px solid #ff8033;
                  color: #ff8033;
                }
                .current-tab {
                  font-size: 14px;
                  border-bottom: 3px solid $button-color;
                  color: #195c94;
                  @media screen and (max-width: 500px) {
                    font-size: 8px !important;
                  }
                  @media screen and (max-width: 768px) {
                    font-size: 12px !important;
                  }
                }

                .search-input {
                  display: flex;
                  align-items: center;
                  width: 50%;
                }

                .history-filter {
                  .radio-container {
                    height: 40px;
                    padding-left: 30px;
                    display: flex;
                    gap: 10px;
                    vertical-align: middle;
                    align-items: flex-start;
                    position: relative;
                    bottom: 6px;
                    .radio-margin {
                      label {
                        position: relative;
                        top: 2px;
                        left: 3px;
                        font-size: 16px;
                      }
                    }
                  }
                }
                .tab-button {
                  min-width: 60%;
                  height: 40px;
                  float: left;
                  background: #fff;
                  margin: 0;
                  margin-top: -15px;
                  font-size: 14px;
                  border-radius: 6px;
                  border: 1px solid rgba(0, 0, 0, 0.15);
                  @media screen and (max-width: 500px) {
                    font-size: 8px;
                  }
                  @media screen and (max-width: 768px) {
                    font-size: 12px;
                  }
                  .profile-tab {
                    float: left;
                    text-align: center;
                    height: 38px;
                    line-height: 50px;
                    cursor: pointer;
                    font-size: 14px;
                    color: #c6c6c6;
                    width: 50%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    i {
                      margin-left: 5px;
                    }
                  }
                  .active {
                    border-bottom: 3px solid $button-color;
                  }
                }
                .tab-button .active {
                  border: 1px solid #ff8033;
                  color: #ff8033;
                  background-color: #e5efff;
                  font-weight: 500;
                  border-radius: 5px 0px 0px 5px;
                }
                .tab-button .activeCrop {
                  border: 1px solid #ff8033;
                  color: #ff8033;
                  width: 100% !important;
                  background-color: #e5efff;
                  font-weight: 500;
                  border-radius: 5px 5px 5px 5px !important;
                }
              }
            }

            .reward-points-filter {
              min-width: 42%;
              height: 40px;
            }
          }
        }
        .second-left-column {
          width: 60%;
        }
        .right-column {
          display: flex;
          flex-direction: column;
          justify-content: flex-end;
        }

        .input-group {
          display: flex;
          align-items: center;
          gap: 10px;
          flex-wrap: nowrap;
          justify-content: flex-end;
        }

        .search-input {
          display: flex;
          align-items: center;
          width: 300px;
        }

        .input-group-add {
          display: flex;
          align-items: center;
          width: 100%;
          padding: 8px 12px;
          background-color: #fff;
          border: 1px solid #ff8033;
          border-radius: 5px;
        }

        .input-group-add i {
          color: #ff8033;
        }

        .input-group-add input {
          border: none;
          width: 100%;
          outline: none;
          font-size: 14px;
          margin-left: 5px;
        }

        .input-group-add input:focus {
          border: none;
        }

        .input-group-add img {
          height: 18px;
          cursor: pointer;
        }

        .export-button,
        .filter-button {
          margin-left: 5px;
        }

        .export-button button,
        .filter-button button {
          width: 40px;
          height: 40px;
          background-color: #ff8033;
          color: white;
          border: none;
          border-radius: 5px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .export-button button:hover,
        .filter-button button:hover {
          background-color: #e06c2b;
        }

        .export-button i,
        .filter-button i {
          color: white;
          font-size: 16px;
        }

        .addSchemeButton {
          height: 37px;
          background-color: #ff8033;
          border-radius: 0.25rem;
          border: 1px solid #ff8033;
          color: #fff;
          padding: 0px 14px 0 14px;
          width: 160px;
        }
      }
    }
  }
}

.filter-main-menu-container {
  padding: 20px !important;
  position: relative;
  top: -6px;
  .filter-menu-heading {
    .filter-title-size {
      font-size: 20px;
      font-family: sans-serif;
      color: #222222;
      font-weight: bold;
    }
  }
  .history-filter-menu-container {
    .filter-menu-input {
      margin-right: 8px;
      height: 70px;
      ::ng-deep.mat-form-field-appearance-outline .mat-form-field-outline {
        height: 36px !important;
      }

      ::ng-deep .mat-form-field-appearance-outline .mat-form-field-infix {
        padding: 1em 0 1em 0;
        height: 36px !important;
      }
      ::ng-deep .cdk-text-field-autofill-monitored:not(:-webkit-autofill) {
        animation: cdk-text-field-autofill-end 0s 1ms;
        width: 70%;
        position: relative;
        top: -36px;
      }
      ::ng-deep .mat-icon-button {
        display: inline !important;
        bottom: 29px !important;
        left: 122px !important;
      }

      ::ng-deep
        .mat-form-field-appearance-outline
        .mat-form-field-outline-start {
        background-color: #fff;
        border-left: 1px solid #FF8033;
        border-top: 1px solid #FF8033;
        border-bottom: 1px solid #FF8033;
      }

      ::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline-end {
        background-color: #fff;
        border-top: 1px solid #FF8033;
        border-right: 1px solid #FF8033;
        border-bottom: 1px solid #FF8033;
      }

      ::ng-deep .mat-form-field-flex {
        display: inline-flex;
        align-items: baseline;
        box-sizing: border-box;
        width: 103% !important;
      }

      ::ng-deep
        .mat-form-field-type-mat-date-range-input
        .mat-form-field-infix {
        width: 55% !important;
      }

      .mat-date-range-input {
        display: block;
        width: 100%;
        margin-top: -10px;
        font-size: 13px;
      }

      label {
        color: #222222;
        font-weight: 500;
      }
    }

    .area-filter-container {
      ::ng-deep .dropdown .angular2-multiselect {
        border: 1px solid #667080 !important;
      }
      label {
        color: #222222;
        font-weight: 500;
      }
    }
    .role-filter-container {
      margin-top: 10px;
      label {
        color: #222222;
        font-weight: 500;
      }
    }
  }

  .button-container {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 10%;
    margin-bottom: 1%;
    button {
      height: 35px;
      width: 50%;
      border: none;
      border-radius: 5px;
      cursor: pointer;
    }
    .btn-cancel {
      background-color: #fff;
      color: #FF8033;
      font-family: sans-serif;
      font-weight: 600;
      border: 1px solid #FF8033;
      text-align: center;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .btn-submit {
      text-align: center;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #FF8033;
      color: #fff;
      font-family: $sans-font-family;
      font-weight: 600;
    }
    .btn-submit:disabled {
      cursor: not-allowed;
    }
  }
  ::ng-deep .cuppa-dropdown .selected-list .c-btn {
    padding: 10px !important;
}
}


::ng-deep .cdk-overlay-pane.filter-dialog-container {
  margin-top: 12.2% !important;
  margin-right: 8% !important;
}

:host ::ng-deep .btn-submit:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

@media screen and (max-width: 1060px) {
  .app-container {
    .app-grid-container {
      .user-grid-action {
        .user-grid-action-container {
          width: 100%;
        }
      }
    }
  }
}

@media screen and (max-width: 900px) {
  .app-container {
    .app-grid-container {
      .user-grid-action {
        .user-grid-action-container {
          .date-filter {
            width: 100%;
          }
        }
      }
    }
  }
}

@media screen and (max-width: 800px) {
  .app-container {
    .app-grid-container {
      .user-grid-action {
        .user-grid-action-container {
          width: 100%;
          .date-filter {
            .date-filter-title {
              width: 100% !important;
            }
            .date-filter-content {
              width: 80%;
            }
          }
        }
      }
    }
  }
}

@media screen and (max-width: 400px) {
  .app-container {
    .app-grid-container {
      .user-grid-action {
        .user-grid-action-container {
          width: 100%;
          .date-filter {
            .date-filter-title {
              width: 100% !important;
            }
            .date-filter-content {
              width: 100%;
            }
          }
        }
      }
    }
  }
}

@media screen and (max-width: 400px) {
  .app-container {
    .app-grid-container {
      .date-picker-container {
        width: 28%;
        height: 52px;
        mat-form-field {
          width: 92%;
          margin: 3px 0px 0px 12px;
        }
        .cancel-button {
          border: 2px solid #102d69;
        }
        .submit-button {
          color: #fff;
          background-color: #102d69;
        }
      }
    }
  }
}

:host::ng-deep .mat-form-field-flex > .mat-form-field-infix {
  padding: 0.3em 0px !important;
}
:host::ng-deep .mat-form-field-label-wrapper {
  top: -1.5em;
}

:host::ng-deep
  .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float
  .mat-form-field-label {
  transform: translateY(-1.1em) scale(0.75);
  width: 60%;
}

// .input-group {
//   flex-wrap: unset !important;
// }
:host::ng-deep.cdk-text-field-autofill-monitored:not(:-webkit-autofill) {
  margin-bottom: 2px;
}

:host ::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline {
  display: flex;
  position: absolute;
  top: 3px;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  height: 38px;
}

:host ::ng-deep button.mat-focus-indicator.mat-icon-button.mat-button-base {
  justify-content: end !important;
}

:host ::ng-deep .mat-date-range-input-container {
  display: flex;
  align-items: center;
  width: 115%;
}

:host ::ng-deep .mat-date-range-input {
  display: block;
  width: 80%;
  font-size: 15px;
  margin-left: 6px;
}

:host ::ng-deep .mat-form-field-appearance-outline .mat-form-field-flex {
  padding: 0 0.15em 0 0.15em;
  margin-top: -0.25em;
  position: relative;
}

.confirm-dialog-container {
  position: fixed !important;
  top: 17% !important;
  right: 2% !important;
}

/* Background Blur */
/* Background Overlay */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3); /* No blur, just a dark overlay */
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;


  .customer-dd .c-btn .c-remove.clear-all {
      position: relative;
      height: 10px;
      width: 10px;
      margin-left: 50px;
      margin-top: 2px;
    }  
}

/* Popup Container */
.popup-container {
  overflow: auto;
  background: #fff;
  width: 40%;
  padding: 30px 40px; /* Increased padding */
  border-radius: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  max-width: 700px;
  max-height: 80%;
}

/* Ensure consistent padding in all popup containers */
.popup-container-appover {
  width: 70% !important;
  max-width: 1000px !important;
  max-height: 85vh;
  padding: 30px !important; /* Added padding */
  overflow: hidden;
  background: #fff;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  margin: 0 auto;
  
  /* Add horizontal scrollbar styles */
  overflow-x: auto;
  overflow-y: hidden; /* Prevent vertical scrolling */
  white-space: nowrap; /* Prevent content wrapping */
  
  /* Firefox scrollbar */
  scrollbar-width: thin;
  scrollbar-color: #FF8033 #f1f1f1;
  
  /* WebKit browsers (Chrome, Safari) scrollbar */
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px; /* Height for horizontal scrollbar */
    background: #f1f1f1;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: #FF8033;
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-track {
    background-color: #f1f1f1;
    border-radius: 4px;
  }
  
  /* For Internet Explorer */
  -ms-overflow-style: auto;
  
  /* Ensure content inside has proper layout for horizontal scrolling */
  .popup-content {
    display: inline-block;
    min-width: 100%;
  }
}

/* Additional styles for horizontal scrolling content */
.popup-container-appover {
  /* Allow normal text wrapping inside content elements */
  .popup-header, 
  .popup-content > div,
  .popup-footer {
    white-space: normal;
  }
  
  /* Ensure tables can scroll horizontally */
  table {
    min-width: 100%;
    table-layout: auto;
  }
}

/* If you need both horizontal and vertical scrolling for specific content */
.popup-container-appover .scrollable-content {
  overflow: auto;
  max-height: 50vh;
  white-space: normal;
}

/* Add padding to popup sections */
.popup-header {
  padding-bottom: 20px !important;
}

.popup-form {
  padding: 10px 0 !important;
}

.popup-footer {
  padding: 25px 0 30px 0 !important;
}

/* Header */
.popup-header h3 {
  margin: 0;
  padding-bottom: 10px !important;
  font-size: 20px;
  font-weight: bold;
  font-family: $sans-font-family;
}

/* Form Styling */
.popup-form {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 15px;
}

.form-group {
  width: 48%;
}

.form-group.full-width {
  width: 100%;
}

.form-group label {
  display: block;
  font-weight: 500;
  margin-bottom: 5px;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 5px;
}

/* Footer Buttons */
.popup-footer {
  display: flex;
  justify-content: center;
  gap: 20px;
  padding: 25px 0 30px 0 !important; /* Increased padding */
  margin-top: 15px;
  
  button {
    min-width: 150px !important; /* Increased button width */
    padding: 12px 24px !important; /* Increased button padding */
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    border: none;
    
    &.cancel-btn {
      background-color: #fff;
      color: #FF8033;
      border: 1px solid #FF8033;
    }
    
    &.next-btn {
      background-color: #FF8033;
      color: #fff;
    }
  }
}
.popup-footer-product {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin: 20px;
  .product-cancel-btn {
    width: 130px;
    background-color: #FF8033 ;
    padding: 10px 35px;
    color: #FFf;
    font-family: sans-serif;
    font-weight: 600;
    border: 1px solid #FF8033;
    border-radius: 5px;
    cursor: pointer;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .popup-footer .cancel-btn:hover {
    background-color: rgba(255, 128, 51, 0.05);
  }
}
.btn-cancel {
  background: #ccc;
  padding: 10px 35px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.btn-submit {
  background: #ff6600;
  color: #fff;
  padding: 10px 35px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  
  &.disabled {
    opacity: 0.5;
    cursor: default !important;
    pointer-events: none;
  }
  
}

.btn-submit:hover {
  background: #e65c00;
}
::ng-deep .cuppa-dropdown .selected-list .c-btn {
  width: 100% !important;
}

.form-group-row {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  margin-bottom: 1rem;

  span {
    // flex: 1 1 30%;
    // min-width: 250px;
    width: 96%;
    label {
      display: block;
      margin-bottom: 4px;
      font-weight: 500;
    }

    .input-field {
      width: 100%;
    }

    ::ng-deep .angular2-multiselect {
      width: 100%;
    }
  }
}

.required {
  color: red;
}

.invoice-container {
  padding: 20px;
  border-radius: 8px;
  width: 100%;
  margin: 0 auto;
  background-color: #fff;

  form {
    display: flex;
    flex-direction: column;
    // gap: 16px;

    .buttons {
      display: flex;
      justify-content: center;
      gap: 10px;
      padding-top: 20px;

      .cancel, .submit {
        padding: 8px 20px;
        border-radius: 4px;
        border: 1px solid #FF8033;
        font-weight: 600;
      }

      .cancel {
        background-color: #fff;
        color: #FF8033;
      }

      .submit {
        background-color: #FF8033;
        color: #fff;
      }
    }
  }
}

::ng-deep .custom-dialog-container .mat-mdc-dialog-surface{
  width: 80% !important;
  padding: 10px 20px;
  max-height: 87%;
  overflow: auto;
}


  //upload product table styling
  mat-table {
    width: 100%;
    border-collapse: collapse;
  
    mat-header-cell, mat-cell {
      padding: 8px;
      text-align: left;
      border: 1px solid #ddd;
      color: #080808; // All table body font color
    }
  
    mat-header-cell {
      background-color: #FBE3D4; // Table header background color
      font-weight: bold;
    }
  
    mat-cell {
      background-color: #ffffff;
      background-color: #ffffff;
      border-bottom: none;
      border-top: none; 
    }
  
    // Alternating row colors
    mat-row:nth-child(even) mat-cell {
      background-color: #F8F8F8; // Second row color
    }
  
    .header-container {
      display: flex;
      align-items: center;
      // gap: 10px;
    }
  
    .cell-container {
      display: flex;
      align-items: center;
      // gap: 10px;
    }
  
    .search-field {
      width: 100% !important;
      
      ::ng-deep .angular2-multiselect {
        .c-btn {
          width: 100% !important;
          min-width: 200px !important; /* Ensure minimum width */
        }
        
        .dropdown-list {
          width: 100% !important;
          min-width: 200px !important;
        }
      }
    }
  
    .custom-multiselect {
      width: 100px;
      .c-btn {
        padding: 5px;
        border: 1px solid #ddd;
      }
    }
  
    // Checkbox styling
    /* Default state: white background, grey border */
    mat-checkbox .mdc-checkbox__background {
      background-color: white !important;
      border: 2px solid grey !important;
    }

    /* Checked state: orange background with white tick */
    mat-checkbox.mat-mdc-checkbox-checked .mdc-checkbox__background {
      background-color: #FF8033 !important;
      border-color: #FF8033 !important;
    }

    mat-checkbox.mat-mdc-checkbox-checked .mdc-checkbox__checkmark-path {
      stroke: white !important;
    }
    
  }

  .button-group {
    display: flex;
    justify-content: center;
    margin-top: 1.5rem;    /* Reduced from 2rem */
    margin-bottom: 1.5rem; /* Reduced from 2rem */
    gap: 10px;

    .cancel-btn,
    .create-btn {
      width: 20%;          /* Reduced from 30% */
      padding: 0.4rem;     /* Reduced from 0.5rem */
      border-radius: 4px;
      font-weight: 500;
      border: 1px solid #FF8033;
      font-size: 14px;     /* Added font-size */
    }

    .cancel-btn {
      background-color: white;
      color: #FF8033;
    }

    .create-btn {
      background-color: #FF8033;
      color: white;
    }
  }

/* Add media query for mobile responsiveness */
@media screen and (max-width: 768px) {
  .button-group {
    .cancel-btn,
    .create-btn {
      width: 40%;        /* Wider on mobile */
    }
  }
}

::ng-deep .mdc-checkbox__background{
  border-color: #1C1B1F !important;
}

::ng-deep .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background{
  background-color: #000000 !important;
  fill: #000000 !important;
}

/* Set popup width to 70% */
.popup-container-appover {
  width: 70% !important;  /* Changed to 70% */
  max-width: 1000px !important;  /* Adjusted max-width */
  max-height: 85vh;
  padding: 0 !important;
  overflow: hidden;
  background: #fff;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  margin: 0 auto;
}

/* Ensure the dialog container also respects the new width */
::ng-deep .custom-dialog-container .mat-mdc-dialog-surface {
  width: 70% !important;
  max-width: 1000px !important;
}

.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.reject-dialog {
  padding: 15px;
  
  .reject-title {
    font-size: 18px;
    margin-bottom: 15px;
    color: #333;
  }
  
  .reject-content {
    margin-bottom: 20px;
    
    .reject-textarea {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      resize: none;
      font-family: inherit;
      
      &:focus {
        outline: none;
        border-color: #FF8033;
      }
    }
  }
  
  .reject-actions {
    display: flex;
    justify-content: center;
    justify-content: center;
    gap: 10px;
    
    button {
      padding: 8px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 500;
      
      &.btn-cancel {
        background-color: #fff;
        color: #FF8033;
        font-family: sans-serif;
        font-weight: 600;
        border: 1px solid #FF8033;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      
      &.btn-submit {
        background-color: #FF8033;
        border: 1px solid #FF8033;
        color: white;
        &:hover {
          background-color: #FF8033;
        }
      }

 
    }
  }
}
:host ::ng-deep .form-group-row .dropdown-list{
  width: 13.5% !important;
}

/* Comprehensive dropdown styling */
::ng-deep .angular2-multiselect {
  width: 100% !important;
  height: 42px !important;
  .c-btn {
    width: 100% !important;
    min-width: 200px !important;
    border: 1px solid #FF8033 !important;
    border-radius: 4px !important;
    padding: 8px 12px !important;
    background-color: #fff !important;
    height: 70px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    font-size: 14px !important;
    color: #464a4c !important;


    
    &:focus, &:active {
      outline: none !important;
      border-color: #FF8033 !important;
      box-shadow: 0 0 0 2px rgba(255, 128, 51, 0.25) !important;
    }
    
    span {
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      white-space: nowrap !important;
      max-width: calc(100% - 30px) !important;
    }
    
    .c-angle-down {
      width: 20px !important;
      height: 20px !important;
      background-color: #fff0eb !important;
      border-radius: 50% !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      
      svg {
        width: 12px !important;
        height: 12px !important;
        fill: #FF8033 !important;
      }
    }
  }
  
  .dropdown-list {
    width: 100% !important;
    min-width: 200px !important;
    border: 1px solid #ccc !important;
    border-radius: 4px !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
    z-index: 9999 !important;
    
    .list-area {
      max-height: 250px !important;
      overflow-y: auto !important;
      
      .pure-checkbox {
        padding: 8px 10px !important;
        
        &:hover {
          background-color: #f5f5f5 !important;
        }
        
        label {
          font-size: 14px !important;
          color: #333 !important;
        }
      }
    }
    
    .search-box {
      padding: 8px !important;
      
      input {
        border: 1px solid #ddd !important;
        border-radius: 4px !important;
        padding: 6px 10px !important;
        width: 100% !important;
        
        &:focus {
          border-color: #FF8033 !important;
          outline: none !important;
        }
      }
    }
  }
  
  .selected-item {
    background: #FF8033 !important;
    border: 1px solid #FF8033 !important;
    color: white !important;
  }
}

/* Specific fix for the invoice preview popup */


/* Custom Product Dropdown Styles */
.product-selection-table {
  margin: 20px 0;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: auto;
  max-height: 300px;

  scrollbar-width: thin;
  scrollbar-color: #FF8033 #f1f1f1;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    background: #f1f1f1;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #FF8033;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-track {
    background-color: #f1f1f1;
    border-radius: 4px;
  }

  .mat-table {
    width: 100%;
    table-layout: fixed; // Prevents column width shifting
  }
  // Center-align header and data content
  .mat-header-cell, .mat-cell {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    text-align: center !important;
    padding: 10px;
    white-space: normal;
  }

  // Define widths
  .mat-column-productName,
  .mat-column-searchBy {
    width: 40% !important;
    min-width: 260px;
  }
  .mat-column-searchBy .search-field {
    width: 100% !important;

    ::ng-deep angular2-multiselect {
      width: 100% !important;

      .c-btn {
        width: 100% !important;
        .span{
          display: flex !important;
          justify-content: center !important;
          .span{
           display: flex;
           padding :3px !important;
          }
         }
      }

  
    }
  }

  .mat-column-quantity,
  .mat-column-unitCost,
  .mat-column-amount {
    width: 13.33% !important;
    min-width: 80px;
    justify-content: center !important;
  }

  .mat-column-productName .cell-container,
  .mat-column-searchBy .cell-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;

    mat-checkbox {
      margin-right: 8px;
      flex-shrink: 0;
    }
  }

  .mat-header-row {
    background-color: #FBE3D4 !important;
    position: sticky;
    top: 0;
    z-index: 10;
  }

  .mat-row {
    &:nth-child(even) {
      background-color: #fafafa;
    }

    &:hover {
      background-color: #f0f0f0;
    }
  }

  .search-field {
    width: 100%;
    max-width: 250px;
  }
}

// Add this to ensure the buttons are properly styled
.buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  // margin-top: 20px;
  
  .cancel, .submit {
    padding: 4px 20px;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
  }
  
  .cancel {
    background-color: white;
    color: #FF8033;
    border: 1px solid #FF8033;
  }
  
  .submit {
    background-color: #FF8033;
    color: white;
    border: 1px solid #FF8033;
  }
}
// ::ng-deep .search-field .selected-list .c-btn span span {
//   display: inline-block;
//   max-width: 73%;
//   overflow: hidden;
//   white-space: nowrap;
//   text-overflow: ellipsis;
//   vertical-align: middle;
// }

::ng-deep .popup-container {
  .form-group {
    .dropdown-icon-level, .dropdown-icon {
      angular2-multiselect {
        // Force borders to be visible with high specificity
        ::ng-deep .c-btn {
          border: 1px solid gray !important;
          border-radius: 4px !important;
          min-height: 36px !important;
          display: flex !important;
          align-items: center !important;
          background-color: #ffffff !important;
          
          &:focus, &:active {
            border-color: #000000 !important;
            box-shadow: 0 0 0 1px rgba(255, 128, 51, 0.25) !important;
          }
        }
        
        // Fix dropdown list styling
        ::ng-deep .dropdown-list {
          border: 1px solid #ccc !important;
          border-radius: 4px !important;
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15) !important;
        }
      }
    }
  }
}
  // Style for enabled/disabled dropdowns
  .enabled-dropdown {
    opacity: 1;
    cursor: pointer;
  }

  .disabled-dropdown {
    opacity: 0.6;
    cursor: not-allowed;
  }

  // Make sure the category dropdown is visible even when disabled
  ::ng-deep .mat-select-disabled .mat-select-value {
    color: rgba(0, 0, 0, 0.6) !important;
  }

/* Improve search input styling for better cross-browser support */
.search-input {
  display: flex;
  align-items: center;
  width: 300px;
  
  .input-group-add {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 8px 12px;
    background-color: #fff;
    border: 1px solid #FF8033;
    border-radius: 5px;
    
    i {
      color: #FF8033;
      margin-right: 8px;
    }
    
    input {
      flex: 1;
      border: none;
      outline: none;
      background: transparent;
      font-size: 14px;
      
      &::placeholder {
        color: #999;
      }
      
      &:focus {
        outline: none;
      }
    }
    
    img {
      height: 16px;
      width: 16px;
      cursor: pointer;
      margin-left: 8px;
    }
  }
}

/* Add specific browser compatibility fixes */
@supports (-webkit-appearance: none) {
  /* Safari and Chrome specific fixes */
  .search-input .input-group-add {
    display: -webkit-flex;
    -webkit-align-items: center;
  }
  
  .search-input input {
    -webkit-appearance: none;
    margin: 0;
  }
}

@supports (-moz-appearance: none) {
  /* Firefox specific fixes */
  .search-input .input-group-add {
    display: -moz-box;
    -moz-box-align: center;
  }
}

/* IE11 specific fixes */
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
  .search-input .input-group-add {
    display: flex;
  }
  
  .search-input input {
    width: 100%;
  }
}
 

::ng-deep .c-btn{
background: #fff;
border: 1px solid #ccc;
color: #333;
}

/* Column width adjustments for approveInvoice template */
/* Column width adjustments for approveInvoice template */
.popup-container-appover {
  /* Ensure the table takes up full width */
  .mat-table {
    width: 100%;
    table-layout: fixed !important;
    min-width: 900px !important; /* Force minimum width to ensure columns don't shrink too much */
  }

  /* Set specific widths for each column */
  .mat-column-productName {
    width: 30% !important;
    min-width: 250px !important;
  }

  .mat-column-quantity,
  .mat-column-unitCost,
  .mat-column-amount,
  .mat-column-category {
    width: 10% !important;
    min-width: 80px !important;
  }

  .mat-column-searchBy {
    width: 30% !important;
    min-width: 250px !important;
  }

  /* Center align all header and cell content */
  .mat-header-cell,
  .mat-cell {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    text-align: center !important;
    padding: 8px;
    white-space: normal;
    word-wrap: break-word;
  }

  /* Header specific styles */
  .mat-header-cell {
    font-weight: 600 !important;
    color: #333 !important;
    background-color: #FBE3D4 !important;
  }

  /* Fix row flex issues */
  .mat-header-row,
  .mat-row {
    display: flex !important;
    width: 100% !important;
  }

  .mat-header-cell,
  .mat-cell {
    flex: none !important;
  }

  /* Sticky header (optional) */
  .mat-header-row {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: #FBE3D4;
  }

  /* Style for product name column with checkbox */
  .mat-column-productName .cell-container {
    display: flex;
    align-items: center;

    mat-checkbox {
      margin-right: 8px;
      flex-shrink: 0;
    }
  }

  /* Style for search by column with dropdown */
  .mat-column-searchBy .search-field {
    width: 100% !important;

    ::ng-deep angular2-multiselect {
      width: 100% !important;

      .c-btn {
        width: 100% !important;
      }
    }
  }

  /* Scroll container for both X and Y */
  .horizontal-scroll-container {
    overflow-x: auto;
    overflow-y: auto;
    max-height: 300px; // Adjust based on your layout
    width: 100%;

    /* Firefox scrollbar */
    scrollbar-width: thin;
    scrollbar-color: #FF8033 #f1f1f1;

    /* WebKit scrollbar */
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
      background: #f1f1f1;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #FF8033;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-track {
      background-color: #f1f1f1;
      border-radius: 4px;
    }

    -ms-overflow-style: auto;
  }

  /* Optional header container alignment (if used in template) */
  .header-container {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
  }

  /* No result message styling */
  .no-result {
    width: 100%;
    text-align: center;
    padding: 20px;
    background-color: white;
    font-size: 14px;
    color: #333;
    border: 1px solid #ddd;
    margin-top: 10px;
    border-radius: 4px;
  }
}

.cust-multiselect .list-item.selected {
  background-color: #e0f7fa !important; /* light blue */
  font-weight: bold;
}
 

/* Force Angular Material to respect our column widths */
.popup-container-appover .mat-table .mat-header-row,
.popup-container-appover .mat-table .mat-row {
  display: flex !important;
  min-width: 100% !important;
}

.popup-container-appover .mat-table .mat-header-cell,
.popup-container-appover .mat-table .mat-cell {
  flex: none !important; /* Override Angular Material's flex behavior */
}

/* Ensure horizontal scrolling works properly */
.popup-container-appover .horizontal-scroll-container {
  overflow-x: auto;
  width: 100%;
  
  /* Firefox scrollbar */
  scrollbar-width: thin;
  scrollbar-color: #FF8033 #f1f1f1;
  
  /* WebKit browsers (Chrome, Safari) scrollbar */
  &::-webkit-scrollbar {
    height: 6px;
    background: #f1f1f1;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: #FF8033;
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-track {
    background-color: #f1f1f1;
    border-radius: 4px;
  }
  
  /* For Internet Explorer */
  -ms-overflow-style: auto;
  
  /* Ensure the table has a minimum width */
  .mat-table {
    min-width: 900px !important; /* Force minimum width to ensure columns don't shrink too much */
  }
}

/* Document Viewer Styles */
.document-viewer-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.document-viewer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  
  h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
    color: #333;
  }
  
  .close-button {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
    
    &:hover {
      color: #343a40;
    }
  }
}

.document-viewer-content {
  flex: 1;
  overflow: hidden;
  background-color: #f5f5f5;
}

.document-frame {
  width: 100%;
  height: 100%;
  border: none;
}

/* Dialog styles */
::ng-deep .document-viewer-dialog {
  .mat-dialog-container {
    padding: 0 !important;
    overflow: hidden !important;
    border-radius: 8px !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2) !important;
  }
}

/* Ensure the dialog takes up the specified space */
::ng-deep .document-viewer-dialog {
  .mat-dialog-content {
    max-height: none !important;
    margin: 0 !important;
    padding: 0 !important;
  }
}

/* Match the iframe container styling from data-table component */
.iframe-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: white;
  z-index: 9999;
  display: flex;
  flex-direction: column;
}

.iframe-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  
  h4 {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
  }
  
  .close-iframe {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
    
    &:hover {
      color: #343a40;
    }
  }
}

/* Ensure the document frame takes up the full height */
.document-frame {
  width: 100%;
  height: calc(100% - 60px);
  border: none;
  background-color: white;
}

.btn-submit:disabled {
  opacity: 0.6; /* Reduce opacity */
  cursor: default !important;
}


// ::ng-deep .search-field.customer-dd.myclass.ng-valid.ng-touched.ng-dirty .cuppa-dropdown .selected-list .c-btn > span {
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   padding: 6px 10px;
//   gap: 5px; /* spacing between child elements */
//   background-color: #f8f8f8;
//   border: 1px solid #ccc;
//   border-radius: 4px;
//   font-family: 'Segoe UI', sans-serif;
//   font-size: 14px;
//   cursor: pointer;
//   transition: background-color 0.3s ease;
// }
